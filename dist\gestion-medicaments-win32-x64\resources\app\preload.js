const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Exposer les APIs de manière sécurisée au processus de rendu
contextBridge.exposeInMainWorld('electronAPI', {
  // Articles
  getAllArticles: () => ipcRenderer.invoke('get-all-articles'),
  getArticleById: (id) => ipcRenderer.invoke('get-article-by-id', id),
  searchArticles: (searchTerm) => ipcRenderer.invoke('search-articles', searchTerm),
  addArticle: (article) => ipcRenderer.invoke('add-article', article),
  updateArticle: (id, article) => ipcRenderer.invoke('update-article', id, article),
  deleteArticle: (id) => ipcRenderer.invoke('delete-article', id),

  // Bons de livraison
  getAllBons: () => ipcRenderer.invoke('get-all-bons'),
  getBonWithLignes: (id) => ipcRenderer.invoke('get-bon-with-lignes', id),
  addBon: (bon) => ipc<PERSON>enderer.invoke('add-bon', bon),
  updateBon: (id, bon) => ipcRenderer.invoke('update-bon', id, bon),
  deleteBon: (id) => ipcRenderer.invoke('delete-bon', id),

  // Lignes de livraison
  addLigneLivraison: (ligne) => ipcRenderer.invoke('add-ligne-livraison', ligne),
  updateLigneLivraison: (id, ligne) => ipcRenderer.invoke('update-ligne-livraison', id, ligne),
  deleteLigneLivraison: (id) => ipcRenderer.invoke('delete-ligne-livraison', id),

  // Statistiques et export
  getStatistiques: () => ipcRenderer.invoke('get-statistiques'),
  exportToCSV: () => ipcRenderer.invoke('export-to-csv'),

  // Événements
  onRefreshData: (callback) => ipcRenderer.on('refresh-data', callback),
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
});
