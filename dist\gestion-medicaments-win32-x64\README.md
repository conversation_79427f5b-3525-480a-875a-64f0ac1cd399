# Gestion des Médicaments - Application Desktop

Application desktop pour la gestion et le comptage des médicaments dans les bons de livraison fournisseur.

## Fonctionnalités

- ✅ **Interface avec 5 colonnes** : NumeroBL, DateBL, MontantBL, Article, Quantité
- ✅ **Gestion complète des bons de livraison**
- ✅ **Ajout/modification/suppression** des médicaments
- ✅ **Calcul automatique** des montants
- ✅ **Recherche et filtrage** des données
- ✅ **Statistiques en temps réel**
- ✅ **Export CSV** pour Excel
- ✅ **Sauvegarde automatique** des données
- ✅ **Interface moderne et responsive**

## Installation et utilisation

### Prérequis
- Node.js (version 16 ou supérieure)
- npm

### Installation des dépendances
```bash
npm install
```

### Lancement en mode développement
```bash
npm start
```

### Construction de l'exécutable Windows
```bash
npm run build-win
```

L'exécutable sera créé dans le dossier `dist/`.

## Structure du projet

```
gestion-medicaments-desktop/
├── main.js              # Processus principal Electron
├── index.html           # Interface utilisateur
├── styles.css           # Styles CSS
├── script.js            # Logique JavaScript
├── package.json         # Configuration du projet
├── assets/              # Ressources (icônes, etc.)
└── README.md           # Documentation
```

## Utilisation de l'application

1. **Créer un nouveau bon** : Cliquez sur "Nouveau Bon" ou utilisez Ctrl+N
2. **Remplir les informations** : Numéro BL, Date, Fournisseur
3. **Ajouter des médicaments** : Article, Quantité, Prix unitaire
4. **Enregistrer** : Le montant total est calculé automatiquement
5. **Visualiser** : Tous les bons apparaissent dans le tableau
6. **Rechercher** : Utilisez la barre de recherche pour filtrer
7. **Exporter** : Ctrl+E pour exporter en CSV

## Raccourcis clavier

- `Ctrl+N` : Nouveau bon de livraison
- `Ctrl+E` : Exporter les données
- `Ctrl+Q` : Quitter l'application
- `F11` : Plein écran
- `F12` : Outils de développement

## Données

Les données sont sauvegardées automatiquement dans le stockage local de l'application.
Elles persistent entre les sessions.

## Support

Pour toute question ou problème, veuillez contacter le développeur.
