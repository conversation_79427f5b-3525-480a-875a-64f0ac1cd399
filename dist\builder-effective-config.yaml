directories:
  output: dist
  buildResources: build
appId: com.gestion.medicaments
productName: Gestion Médicaments
files:
  - filter:
      - '**/*'
      - '!node_modules/**/*'
      - '!dist/**/*'
      - '!build/**/*'
      - '!.git/**/*'
      - '!*.md'
      - '!build-portable.js'
      - '!build.bat'
extraFiles:
  - from: database.db
    to: database.db
    filter:
      - '**/*'
win:
  target:
    - target: portable
      arch:
        - x64
    - target: nsis
      arch:
        - x64
  icon: assets/icon.ico
portable:
  artifactName: ${productName}-${version}-portable.${ext}
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  artifactName: ${productName}-${version}-setup.${ext}
  displayLanguageSelector: true
  installerLanguages:
    - fr_FR
    - en_US
electronVersion: 28.3.3
