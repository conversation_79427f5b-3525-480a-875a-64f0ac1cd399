// Données de l'application
let bonsLivraison = [];
let articles = [];
let currentEditingId = null;
let medicamentsTemp = []; // Toujours initialiser comme array
let currentBonId = null;

// Fonction pour sécuriser les arrays
function ensureArray(variable, name) {
    if (!variable || !Array.isArray(variable)) {
        console.warn(`${name} n'est pas un array valide, réinitialisation:`, variable);
        return [];
    }
    return variable;
}

// Fonction pour vérifier l'intégrité des données
function checkDataIntegrity() {
    bonsLivraison = ensureArray(bonsLivraison, 'bonsLivraison');
    articles = ensureArray(articles, 'articles');
    medicamentsTemp = ensureArray(medicamentsTemp, 'medicamentsTemp');

    console.log('Vérification intégrité des données:', {
        bonsLivraison: bonsLivraison.length,
        articles: articles.length,
        medicamentsTemp: medicamentsTemp.length
    });
}

// Éléments DOM
const formSection = document.getElementById('form-section');
const bonForm = document.getElementById('bon-form');
const tableBody = document.getElementById('table-body');
const emptyState = document.getElementById('empty-state');
const searchInput = document.getElementById('search-input');

// Boutons
const btnNouveau = document.getElementById('btn-nouveau');
const btnCancel = document.getElementById('btn-cancel');
const btnAddMedicament = document.getElementById('btn-add-medicament');
const btnExport = document.getElementById('btn-export');
const btnImportArticles = document.getElementById('btn-import-articles');

// Champs du formulaire
const numeroBLInput = document.getElementById('numeroBL');
const dateBLInput = document.getElementById('dateBL');
const fournisseurInput = document.getElementById('fournisseur');
const articleInput = document.getElementById('article');
const quantiteInput = document.getElementById('quantite');
const prixUnitaireInput = document.getElementById('prixUnitaire');
const medicamentsList = document.getElementById('medicaments-list');
const formTitle = document.getElementById('form-title');
const articleSuggestions = document.getElementById('article-suggestions');

// Statistiques
const totalBonsEl = document.getElementById('total-bons');
const totalMontantEl = document.getElementById('total-montant');
const totalQuantiteEl = document.getElementById('total-quantite');
// totalArticlesEl supprimé car section "Articles Différents" retirée

// Initialisation
document.addEventListener('DOMContentLoaded', async function() {
    console.log('DOM chargé, initialisation...');

    // Vérifier que tous les éléments DOM sont présents
    const requiredElements = {
        'btn-nouveau': btnNouveau,
        'btn-add-medicament': btnAddMedicament,
        'form-section': formSection,
        'bon-form': bonForm,
        'numeroBL': numeroBLInput,
        'dateBL': dateBLInput,
        'article': articleInput,
        'quantite': quantiteInput
    };

    for (const [id, element] of Object.entries(requiredElements)) {
        if (!element) {
            console.error(`Élément manquant: ${id}`);
        }
    }

    await loadData();
    await loadArticles();
    updateDisplay();
    setupEventListeners();

    // Définir la date d'aujourd'hui par défaut
    if (dateBLInput) {
        dateBLInput.value = new Date().toISOString().split('T')[0];
    }

    // Écouter les événements de rafraîchissement
    if (window.electronAPI) {
        window.electronAPI.onRefreshData(() => {
            loadData();
            loadArticles();
            updateDisplay();
        });
    }

    console.log('Initialisation terminée');
});

// Configuration des écouteurs d'événements
function setupEventListeners() {
    console.log('Configuration des écouteurs d\'evénements...');

    if (btnNouveau) {
        btnNouveau.addEventListener('click', () => {
            console.log('Bouton Nouveau cliqué');
            showForm();
        });
    } else {
        console.error('btnNouveau non trouvé');
    }

    if (btnCancel) {
        btnCancel.addEventListener('click', hideForm);
    }

    if (btnAddMedicament) {
        btnAddMedicament.addEventListener('click', () => {
            console.log('Bouton Ajouter Médicament cliqué');
            addMedicament();
        });
    } else {
        console.error('btnAddMedicament non trouvé');
    }

    if (btnExport) {
        btnExport.addEventListener('click', exportData);
    }

    if (btnImportArticles) {
        btnImportArticles.addEventListener('click', importArticles);
    }

    if (bonForm) {
        bonForm.addEventListener('submit', handleFormSubmit);
    }

    if (searchInput) {
        searchInput.addEventListener('input', handleSearch);
    }

    // Recherche d'articles
    if (articleInput) {
        articleInput.addEventListener('input', handleArticleSearch);
        articleInput.addEventListener('focus', handleArticleSearch);
        articleInput.addEventListener('blur', () => {
            setTimeout(() => hideSuggestions(), 200);
        });
    }

    console.log('Écouteurs d\'evénements configurés');
}

// Afficher le formulaire
function showForm(editData = null) {
    currentEditingId = editData ? editData.id : null;
    medicamentsTemp = []; // Réinitialiser la liste temporaire

    formTitle.textContent = editData ? 'Modifier le Bon de Livraison' : 'Nouveau Bon de Livraison';

    if (editData) {
        numeroBLInput.value = editData.numeroBL;
        dateBLInput.value = editData.dateBL;
        fournisseurInput.value = editData.fournisseur || '';
        // Charger les médicaments du bon en mode édition
        loadBonMedicaments(editData.id);
    } else {
        bonForm.reset();
        dateBLInput.value = new Date().toISOString().split('T')[0];
    }

    updateMedicamentsList();
    formSection.classList.remove('hidden');
    numeroBLInput.focus();
}

// Charger les médicaments d'un bon pour l'édition
async function loadBonMedicaments(bonId) {
    console.log('=== DEBUG LOAD BON MEDICAMENTS ===');
    console.log('Chargement des médicaments pour BL ID:', bonId);

    try {
        if (window.electronAPI) {
            const bonDetails = await window.electronAPI.getBonWithLignes(bonId);
            console.log('Détails BL pour édition:', bonDetails);

            if (bonDetails && bonDetails.lignes) {
                console.log('Lignes trouvées:', bonDetails.lignes.length);

                // Charger tous les articles disponibles pour la recherche
                let articlesDisponibles = [];
                try {
                    if (window.electronAPI && window.electronAPI.getAllArticles) {
                        articlesDisponibles = await window.electronAPI.getAllArticles();
                        console.log('Articles chargés pour recherche:', articlesDisponibles.length);
                    }
                } catch (error) {
                    console.error('Erreur lors du chargement des articles:', error);
                }

                medicamentsTemp = bonDetails.lignes.map((ligne, index) => {
                    console.log(`=== LIGNE ${index + 1} COMPLETE ===`);
                    console.log('Objet ligne complet:', ligne);
                    console.log('Toutes les clés disponibles:', Object.keys(ligne));

                    // Afficher tous les champs possibles pour le nom
                    console.log('Champs possibles pour nom:');
                    console.log('- article_nom:', ligne.article_nom);
                    console.log('- designation:', ligne.designation);
                    console.log('- nom:', ligne.nom);
                    console.log('- libelle:', ligne.libelle);
                    console.log('- article:', ligne.article);
                    console.log('- nom_article:', ligne.nom_article);
                    console.log('- article_designation:', ligne.article_designation);
                    console.log('- medicament:', ligne.medicament);
                    console.log('- produit:', ligne.produit);

                    // Essayer TOUS les champs possibles pour le nom de l'article
                    let articleNom = ligne.article_nom ||
                                   ligne.designation ||
                                   ligne.nom ||
                                   ligne.libelle ||
                                   ligne.article ||
                                   ligne.nom_article ||
                                   ligne.article_designation ||
                                   ligne.medicament ||
                                   ligne.produit;

                    // Si aucun nom trouvé mais qu'on a un article_id, chercher dans les articles chargés
                    if (!articleNom && ligne.article_id && articlesDisponibles.length > 0) {
                        console.log('Recherche dans les articles pour ID:', ligne.article_id);
                        const articleTrouve = articlesDisponibles.find(a => a.id == ligne.article_id);
                        if (articleTrouve) {
                            articleNom = articleTrouve.designation || articleTrouve.nom || articleTrouve.libelle;
                            console.log('Article trouvé dans la base:', articleNom);
                        }
                    }

                    // Si toujours pas de nom, essayer avec d'autres IDs possibles
                    if (!articleNom && articlesDisponibles.length > 0) {
                        const possibleIds = [ligne.id, ligne.medicament_id, ligne.produit_id];
                        for (const possibleId of possibleIds) {
                            if (possibleId) {
                                const articleTrouve = articlesDisponibles.find(a => a.id == possibleId);
                                if (articleTrouve) {
                                    articleNom = articleTrouve.designation || articleTrouve.nom || articleTrouve.libelle;
                                    console.log(`Article trouvé avec ID ${possibleId}:`, articleNom);
                                    break;
                                }
                            }
                        }
                    }

                    // Fallback final
                    if (!articleNom) {
                        articleNom = `Article ${index + 1}`;
                        console.log('Utilisation du fallback:', articleNom);
                    }

                    console.log(`✅ Article nom final déterminé: "${articleNom}"`);

                    const medicament = {
                        id: ligne.id ? ligne.id.toString() : Date.now().toString() + index,
                        article: articleNom,
                        quantite: ligne.quantite_livree || ligne.quantite || 0,
                        prixUnitaire: ligne.prix_unitaire || ligne.prix || 0,
                        total: ligne.total_ligne || ligne.total || (ligne.quantite_livree || ligne.quantite || 0) * (ligne.prix_unitaire || ligne.prix || 0)
                    };

                    console.log('Médicament créé:', medicament);
                    return medicament;
                });

                console.log('Médicaments temp créés:', medicamentsTemp);
                updateMedicamentsList();
            } else {
                console.warn('Aucune ligne trouvée pour ce BL');
                medicamentsTemp = [];
                updateMedicamentsList();
            }
        } else {
            console.error('electronAPI non disponible');
        }
    } catch (error) {
        console.error('Erreur lors du chargement des médicaments:', error);
        medicamentsTemp = [];
        updateMedicamentsList();
    }
}

// Masquer le formulaire
function hideForm() {
    formSection.classList.add('hidden');
    bonForm.reset();
    currentEditingId = null;
    medicamentsTemp = [];
    updateMedicamentsList();
}

// Ajouter un médicament à la liste temporaire
function addMedicament() {
    const article = articleInput.value.trim();
    const quantite = parseInt(quantiteInput.value);
    const prixUnitaire = parseFloat(prixUnitaireInput.value) || 0;

    if (!article || !quantite || quantite <= 0) {
        alert('Veuillez remplir l\'article et la quantité correctement.');
        return;
    }

    const medicament = {
        id: Date.now().toString(),
        article: article,
        quantite: quantite,
        prixUnitaire: prixUnitaire,
        total: quantite * prixUnitaire
    };

    medicamentsTemp.push(medicament);

    // Réinitialiser les champs
    articleInput.value = '';
    quantiteInput.value = '';
    prixUnitaireInput.value = '';

    updateMedicamentsList();
    articleInput.focus();
}

// Mettre à jour l'affichage de la liste des médicaments
function updateMedicamentsList() {
    medicamentsList.innerHTML = '';

    // UTILISER BOUCLE FOR AU LIEU DE FOREACH POUR ÉVITER L'ERREUR
    for (let index = 0; index < medicamentsTemp.length; index++) {
        const medicament = medicamentsTemp[index];

        if (!medicament) {
            console.warn(`Médicament ${index} est null ou undefined, ignoré`);
            continue;
        }

        const div = document.createElement('div');
        div.className = 'medicament-item';
        div.innerHTML = `
            <div class="medicament-info">
                <strong>${medicament.article}</strong> -
                Quantité: ${medicament.quantite} -
                Prix unitaire: ${medicament.prixUnitaire.toFixed(3)} DT -
                Total: ${medicament.total.toFixed(3)} DT
            </div>
            <button type="button" class="btn btn-danger btn-small" onclick="removeMedicament('${medicament.id}')">
                Supprimer
            </button>
        `;
        medicamentsList.appendChild(div);
    }
}

// Supprimer un médicament de la liste temporaire
function removeMedicament(id) {
    medicamentsTemp = medicamentsTemp.filter(med => med.id !== id);
    updateMedicamentsList();
}

// Recherche d'articles optimisée
function handleArticleSearch() {
    const searchTerm = articleInput.value.trim();

    if (searchTerm.length < 1) {
        hideSuggestions();
        return;
    }

    // Recherche optimisée avec priorité
    const filteredArticles = articles.filter(article =>
        article.designation.toLowerCase().includes(searchTerm.toLowerCase())
    ).sort((a, b) => {
        const term = searchTerm.toLowerCase();
        const aDesignation = a.designation.toLowerCase();
        const bDesignation = b.designation.toLowerCase();

        // Priorité 1: Correspondance exacte
        if (aDesignation === term && bDesignation !== term) return -1;
        if (aDesignation !== term && bDesignation === term) return 1;

        // Priorité 2: Commence par le terme
        const aStartsWith = aDesignation.startsWith(term);
        const bStartsWith = bDesignation.startsWith(term);
        if (aStartsWith && !bStartsWith) return -1;
        if (!aStartsWith && bStartsWith) return 1;

        // Priorité 3: Ordre alphabétique
        return a.designation.localeCompare(b.designation);
    }).slice(0, 8); // Limiter à 8 résultats pour une meilleure UX

    showSuggestions(filteredArticles);
}

function showSuggestions(filteredArticles) {
    if (filteredArticles.length === 0) {
        hideSuggestions();
        return;
    }

    articleSuggestions.innerHTML = '';

    // UTILISER BOUCLE FOR AU LIEU DE FOREACH
    for (let i = 0; i < filteredArticles.length; i++) {
        const article = filteredArticles[i];
        if (!article) continue;

        const div = document.createElement('div');
        div.className = 'suggestion-item';
        div.innerHTML = `
            <div class="article-name">${article.designation}</div>
            <div class="article-details">
                Prix: ${article.prix_unitaire.toFixed(3)} DT
                ${article.stock_actuel > 0 ? ' | Stock: ' + article.stock_actuel : ''}
            </div>
        `;

        div.addEventListener('click', () => {
            selectArticle(article);
        });

        articleSuggestions.appendChild(div);
    }

    articleSuggestions.style.display = 'block';
}

function hideSuggestions() {
    articleSuggestions.style.display = 'none';
}

function selectArticle(article) {
    articleInput.value = article.designation;
    prixUnitaireInput.value = article.prix_unitaire;
    hideSuggestions();
    quantiteInput.focus();
}

// Import d'articles Excel
function importArticles() {
    if (window.electronAPI) {
        // L'import sera géré par le menu principal
        alert('Utilisez le menu Fichier > Importer Articles Excel ou Ctrl+I');
    } else {
        alert('Fonctionnalité d\'import disponible uniquement dans l\'application desktop.');
    }
}

// Gérer la soumission du formulaire - VERSION ULTRA SÉCURISÉE SANS FOREACH
async function handleFormSubmit(e) {
    e.preventDefault();

    console.log('=== DÉBUT SAUVEGARDE ULTRA SÉCURISÉE ===');

    // PROTECTION ABSOLUE - PAS DE FOREACH NULLE PART
    let medicamentsSecurises = [];

    try {
        console.log('Vérification medicamentsTemp:', {
            exists: typeof medicamentsTemp !== 'undefined',
            type: typeof medicamentsTemp,
            isArray: Array.isArray(medicamentsTemp),
            length: medicamentsTemp ? medicamentsTemp.length : 'N/A'
        });

        // Sécurisation SANS FOREACH
        if (!medicamentsTemp || !Array.isArray(medicamentsTemp) || medicamentsTemp.length === 0) {
            console.error('medicamentsTemp invalide, reconstruction depuis DOM');

            // Reconstruction manuelle depuis DOM SANS FOREACH
            const items = document.querySelectorAll('.medicament-item');
            medicamentsSecurises = [];

            for (let i = 0; i < items.length; i++) {
                const item = items[i];
                if (!item) continue;

                const infoDiv = item.querySelector('.medicament-info');
                if (!infoDiv) continue;

                const text = infoDiv.textContent;
                const matches = text.match(/^(.+?) - Quantité: (\d+) - Prix unitaire: ([\d.]+) .* - Total: ([\d.]+)/);

                if (matches) {
                    medicamentsSecurises.push({
                        id: Date.now().toString() + i,
                        article: matches[1].trim(),
                        quantite: parseInt(matches[2]),
                        prixUnitaire: parseFloat(matches[3]),
                        total: parseFloat(matches[4])
                    });
                }
            }
        } else {
            // Copie manuelle SANS FOREACH
            medicamentsSecurises = [];
            for (let i = 0; i < medicamentsTemp.length; i++) {
                if (medicamentsTemp[i]) {
                    medicamentsSecurises.push(medicamentsTemp[i]);
                }
            }
        }

        console.log('Médicaments sécurisés:', medicamentsSecurises.length);

    } catch (error) {
        console.error('Erreur sécurisation:', error);
        alert('ERREUR: Impossible de sécuriser les médicaments.\n\n' + error.message);
        return;
    }

    const numeroBL = numeroBLInput.value.trim();
    const dateBL = dateBLInput.value;
    const fournisseur = fournisseurInput.value.trim();

    if (!numeroBL || !dateBL) {
        alert('Veuillez remplir tous les champs obligatoires.');
        return;
    }

    if (medicamentsSecurises.length === 0) {
        alert('Veuillez ajouter au moins un médicament.');
        return;
    }

    try {
        // Calculer le montant total SANS FOREACH
        let montantBL = 0;
        for (let i = 0; i < medicamentsSecurises.length; i++) {
            if (medicamentsSecurises[i] && medicamentsSecurises[i].total) {
                montantBL += medicamentsSecurises[i].total;
            }
        }

        console.log('Montant total calculé:', montantBL);

        const bonData = {
            numero_bl: numeroBL,
            date_bl: dateBL,
            fournisseur: fournisseur,
            montant_total: montantBL,
            statut: 'En attente',
            notes: ''
        };

        let bonId;

        if (currentEditingId) {
            // Modifier le bon existant
            console.log('Modification du bon existant:', currentEditingId);
            await window.electronAPI.updateBon(currentEditingId, bonData);
            bonId = currentEditingId;
        } else {
            // Créer un nouveau bon
            console.log('Création d\'un nouveau bon');
            const result = await window.electronAPI.addBon(bonData);
            bonId = result.lastInsertRowid;
            console.log('Nouveau bon créé avec ID:', bonId);
        }

        // Ajouter les lignes de livraison SANS FOREACH
        console.log('Ajout des lignes de livraison...');
        for (let i = 0; i < medicamentsSecurises.length; i++) {
            const med = medicamentsSecurises[i];
            if (!med) continue;

            console.log(`Traitement médicament ${i + 1}/${medicamentsSecurises.length}:`, med.article);

            // Trouver l'article correspondant
            const article = articles.find(a => a.designation === med.article);
            if (article) {
                const ligne = {
                    bon_id: bonId,
                    article_id: article.id,
                    quantite_commandee: med.quantite,
                    quantite_livree: med.quantite,
                    prix_unitaire: med.prixUnitaire,
                    total_ligne: med.total
                };

                await window.electronAPI.addLigneLivraison(ligne);
                console.log('Ligne ajoutée pour:', med.article);
            } else {
                console.warn('Article non trouvé:', med.article);
            }
        }

        // Recharger les données et mettre à jour l'affichage
        await loadData();
        updateDisplay();
        hideForm();

        console.log('=== SAUVEGARDE TERMINÉE AVEC SUCCÈS ===');
        alert(currentEditingId ? 'Bon de livraison modifié avec succès!' : 'Bon de livraison ajouté avec succès!');

    } catch (error) {
        console.error('Erreur lors de la sauvegarde:', error);
        alert('Erreur lors de la sauvegarde du bon de livraison:\n\nDétails: ' + error.message);
    }
}

// Mettre à jour l'affichage
function updateDisplay() {
    updateTable();
    updateStats();
    updateEmptyState();
}

// Mettre à jour le tableau - AFFICHAGE UNE LIGNE PAR BL
async function updateTable() {
    tableBody.innerHTML = '';

    const filteredBons = getFilteredBons();

    for (const bon of filteredBons) {
        try {
            // Récupérer les détails du bon avec ses lignes
            const bonDetails = await window.electronAPI.getBonWithLignes(bon.id);

            // Calculer les totaux pour ce BL
            let nbArticles = 0;
            let quantiteTotale = 0;

            if (bonDetails && bonDetails.lignes && bonDetails.lignes.length > 0) {
                nbArticles = bonDetails.lignes.length;

                // Calculer la quantité totale SANS FOREACH
                for (let j = 0; j < bonDetails.lignes.length; j++) {
                    const ligne = bonDetails.lignes[j];
                    if (ligne && ligne.quantite_livree) {
                        quantiteTotale += ligne.quantite_livree;
                    }
                }
            }

            // Créer UNE SEULE ligne par BL
            const row = document.createElement('tr');

            // DEBUG: Vérifier l'ID du bon
            console.log('Création ligne pour BL:', { id: bon.id, type: typeof bon.id, numeroBL: bon.numeroBL });

            row.innerHTML = `
                <td><strong>${bon.numeroBL}</strong></td>
                <td>${formatDate(bon.dateBL)}</td>
                <td>${bon.fournisseur || 'Non spécifié'}</td>
                <td>${bon.montantBL.toFixed(3)} DT</td>
                <td><span class="badge-count">${nbArticles}</span></td>
                <td><span class="badge-quantity">${quantiteTotale}</span></td>
                <td>
                    <div class="actions">
                        <button class="btn btn-edit btn-small" onclick="editBon(${bon.id})" title="Modifier ce BL">
                            ✏️ Modifier
                        </button>
                        <button class="btn btn-danger btn-small" onclick="deleteBon(${bon.id})" title="Supprimer ce BL">
                            🗑️ Supprimer
                        </button>
                    </div>
                </td>
            `;

            tableBody.appendChild(row);

        } catch (error) {
            console.error('Erreur lors du chargement des détails du bon:', error);

            // Afficher le BL même en cas d'erreur
            const row = document.createElement('tr');
            row.innerHTML = `
                <td><strong>${bon.numeroBL}</strong></td>
                <td>${formatDate(bon.dateBL)}</td>
                <td>${bon.fournisseur || 'Non spécifié'}</td>
                <td>${bon.montantBL.toFixed(3)} DT</td>
                <td><span class="badge-error">Erreur</span></td>
                <td><span class="badge-error">Erreur</span></td>
                <td>
                    <div class="actions">
                        <button class="btn btn-edit btn-small" onclick="editBon(${bon.id})">
                            ✏️ Modifier
                        </button>
                        <button class="btn btn-danger btn-small" onclick="deleteBon(${bon.id})">
                            🗑️ Supprimer
                        </button>
                    </div>
                </td>
            `;

            tableBody.appendChild(row);
        }
    }
}

// Obtenir les bons filtrés selon la recherche
function getFilteredBons() {
    const searchTerm = searchInput.value.toLowerCase().trim();

    if (!searchTerm) {
        return bonsLivraison;
    }

    return bonsLivraison.filter(bon =>
        bon.numeroBL.toLowerCase().includes(searchTerm) ||
        bon.fournisseur.toLowerCase().includes(searchTerm) ||
        bon.medicaments.some(med =>
            med.article.toLowerCase().includes(searchTerm)
        )
    );
}

// Mettre à jour les statistiques
async function updateStats() {
    console.log('=== MISE À JOUR DES STATISTIQUES ===');
    console.log('Nombre de BL:', bonsLivraison.length);

    const totalBons = bonsLivraison.length;
    const totalMontant = bonsLivraison.reduce((sum, bon) => sum + (bon.montantBL || 0), 0);

    let totalQuantite = 0;

    // Calculer la quantité totale en utilisant les données réelles des BL
    for (let i = 0; i < bonsLivraison.length; i++) {
        const bon = bonsLivraison[i];
        if (!bon) continue;

        try {
            // Récupérer les détails du BL avec ses lignes
            if (window.electronAPI && window.electronAPI.getBonWithLignes) {
                const bonDetails = await window.electronAPI.getBonWithLignes(bon.id);
                if (bonDetails && bonDetails.lignes) {
                    for (let j = 0; j < bonDetails.lignes.length; j++) {
                        const ligne = bonDetails.lignes[j];
                        if (ligne && ligne.quantite_livree) {
                            totalQuantite += ligne.quantite_livree;
                        }
                    }
                }
            } else {
                // Fallback : utiliser les données locales si disponibles
                if (bon.medicaments) {
                    for (let j = 0; j < bon.medicaments.length; j++) {
                        const med = bon.medicaments[j];
                        if (med && med.quantite) {
                            totalQuantite += med.quantite;
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Erreur lors du calcul des quantités pour BL', bon.id, ':', error);
            // Fallback en cas d'erreur
            if (bon.medicaments) {
                for (let j = 0; j < bon.medicaments.length; j++) {
                    const med = bon.medicaments[j];
                    if (med && med.quantite) {
                        totalQuantite += med.quantite;
                    }
                }
            }
        }
    }

    console.log('Statistiques calculées:');
    console.log('- Total BL:', totalBons);
    console.log('- Montant total:', totalMontant.toFixed(3), 'DT');
    console.log('- Quantité totale:', totalQuantite);

    // Mettre à jour l'affichage
    if (totalBonsEl) totalBonsEl.textContent = totalBons;
    if (totalMontantEl) totalMontantEl.textContent = totalMontant.toFixed(3) + ' DT';
    if (totalQuantiteEl) totalQuantiteEl.textContent = totalQuantite;
}

// Mettre à jour l'état vide
function updateEmptyState() {
    if (bonsLivraison.length === 0) {
        emptyState.classList.remove('hidden');
        document.querySelector('.table-container').style.display = 'none';
    } else {
        emptyState.classList.add('hidden');
        document.querySelector('.table-container').style.display = 'block';
    }
}

// Modifier un bon
function editBon(id) {
    console.log('=== DEBUG EDIT BON ===');
    console.log('ID reçu:', id, 'Type:', typeof id);
    console.log('Nombre de BL disponibles:', bonsLivraison.length);
    console.log('Liste des BL:', bonsLivraison);

    // Essayer de trouver par ID (string et number)
    let bon = bonsLivraison.find(b => b.id == id); // Comparaison souple

    if (!bon) {
        // Essayer de trouver par ID converti en nombre
        const numericId = parseInt(id);
        bon = bonsLivraison.find(b => b.id === numericId);
        console.log('Tentative avec ID numérique:', numericId);
    }

    if (!bon) {
        // Essayer de trouver par ID converti en string
        const stringId = String(id);
        bon = bonsLivraison.find(b => String(b.id) === stringId);
        console.log('Tentative avec ID string:', stringId);
    }

    if (bon) {
        console.log('BL trouvé:', bon);
        console.log('Appel de showForm...');
        showForm(bon);
    } else {
        console.error('BL NON TROUVÉ avec ID:', id);
        console.log('IDs disponibles:', bonsLivraison.map(b => ({ id: b.id, type: typeof b.id, numeroBL: b.numeroBL })));

        // Afficher une alerte détaillée
        alert(`Bon de livraison non trouvé.\n\nID recherché: ${id} (${typeof id})\nNombre de BL: ${bonsLivraison.length}\n\nVérifiez la console (F12) pour plus de détails.`);
    }
}

// S'assurer que les fonctions sont accessibles globalement
window.editBon = editBon;
window.deleteBon = deleteBon;
window.viewBonDetails = viewBonDetails;
window.closeBonDetails = closeBonDetails;

// Fonction de test pour vérifier les actions BL
function testBLActions() {
    console.log('Test des actions BL...');

    if (bonsLivraison.length === 0) {
        alert('🚨 Aucun bon de livraison disponible pour tester.\n\nCréez d\'abord un BL pour tester les actions.');
        return;
    }

    const firstBL = bonsLivraison[0];
    console.log('Premier BL pour test:', firstBL);

    const testChoice = confirm(`🎯 Test des Actions BL\n\nBL de test: ${firstBL.numeroBL}\n\n✅ OK = Tester "Modifier"\n❌ Annuler = Tester "Supprimer"`);

    if (testChoice) {
        console.log('Test de editBon...');
        editBon(firstBL.id);
    } else {
        console.log('Test de deleteBon...');
        if (confirm('Confirmer le test de suppression ?')) {
            deleteBon(firstBL.id);
        } else {
            console.log('Test de suppression annulé');
        }
    }
}

// Rendre la fonction de test accessible globalement
window.testBLActions = testBLActions;

// Fonction de debugging pour vérifier les données BL
function debugBLData() {
    console.log('=== DEBUG DONNÉES BL ===');
    console.log('Nombre de BL:', bonsLivraison.length);
    console.log('Structure des BL:');

    for (let i = 0; i < Math.min(bonsLivraison.length, 3); i++) {
        const bl = bonsLivraison[i];
        console.log(`BL ${i + 1}:`, {
            id: bl.id,
            type_id: typeof bl.id,
            numeroBL: bl.numeroBL,
            dateBL: bl.dateBL,
            fournisseur: bl.fournisseur,
            montantBL: bl.montantBL
        });
    }

    return bonsLivraison;
}

// Rendre la fonction de debug accessible globalement
window.debugBLData = debugBLData;

// Fonction de test spécifique pour les détails
function testBLDetails() {
    console.log('Test spécifique des détails BL...');

    if (bonsLivraison.length === 0) {
        alert('🚨 Aucun bon de livraison disponible pour tester les détails.');
        return;
    }

    const firstBL = bonsLivraison[0];
    console.log('Test détails pour BL:', firstBL);

    alert(`🔍 Test des Détails BL\n\nBL de test: ${firstBL.numeroBL}\nID: ${firstBL.id}\n\nOuverture de la modal...`);

    viewBonDetails(firstBL.id);
}

// Rendre la fonction de test détails accessible globalement
window.testBLDetails = testBLDetails;

// Fonction de test de l'API Electron
function testElectronAPI() {
    console.log('=== TEST API ELECTRON ===');

    // Vérifier la disponibilité de l'API
    console.log('window.electronAPI existe:', !!window.electronAPI);

    if (window.electronAPI) {
        console.log('Méthodes disponibles:', Object.keys(window.electronAPI));

        // Vérifier les méthodes spécifiques
        const methods = [
            'getBonWithLignes',
            'getAllBons',
            'addBon',
            'updateBon',
            'deleteBon',
            'getAllArticles'
        ];

        methods.forEach(method => {
            console.log(`${method}:`, typeof window.electronAPI[method]);
        });

        alert('🔍 Test API Electron\n\nRésultats dans la console (F12)\n\nAPI disponible: ' + (window.electronAPI ? 'OUI' : 'NON'));
    } else {
        alert('⚠️ API Electron NON DISPONIBLE\n\nL\'application ne peut pas communiquer avec la base de données.');
    }

    return window.electronAPI;
}

// Rendre la fonction de test API accessible globalement
window.testElectronAPI = testElectronAPI;

// Fonction pour récupérer le nom d'un article par son ID
async function getArticleNameById(articleId) {
    console.log('Recherche du nom pour article ID:', articleId);

    try {
        if (window.electronAPI && window.electronAPI.getAllArticles) {
            const articles = await window.electronAPI.getAllArticles();
            console.log('Articles disponibles:', articles.length);

            const article = articles.find(a => a.id == articleId);
            if (article) {
                const nom = article.designation || article.nom || article.libelle || `Article ID ${articleId}`;
                console.log(`Article trouvé pour ID ${articleId}:`, nom);
                return nom;
            } else {
                console.log(`Aucun article trouvé pour ID ${articleId}`);
                return `Article ID ${articleId}`;
            }
        } else {
            console.log('API getAllArticles non disponible');
            return `Article ID ${articleId}`;
        }
    } catch (error) {
        console.error('Erreur lors de la recherche d\'article:', error);
        return `Article ID ${articleId}`;
    }
}

// Rendre la fonction accessible globalement
window.getArticleNameById = getArticleNameById;

// Fonction de test pour vérifier les articles
async function testArticlesData() {
    console.log('=== TEST ARTICLES DATA ===');

    try {
        if (window.electronAPI && window.electronAPI.getAllArticles) {
            const articles = await window.electronAPI.getAllArticles();
            console.log('Nombre d\'articles:', articles.length);

            if (articles.length > 0) {
                console.log('Premiers articles:');
                for (let i = 0; i < Math.min(articles.length, 5); i++) {
                    const article = articles[i];
                    console.log(`Article ${i + 1}:`, {
                        id: article.id,
                        designation: article.designation,
                        nom: article.nom,
                        libelle: article.libelle,
                        prix: article.prix
                    });
                }

                alert(`📋 Articles Test\n\nNombre d'articles: ${articles.length}\n\nPremiers articles affichés dans la console (F12)`);
            } else {
                alert('⚠️ Aucun article trouvé dans la base de données.');
            }
        } else {
            alert('⚠️ API getAllArticles non disponible.');
        }
    } catch (error) {
        console.error('Erreur lors du test des articles:', error);
        alert(`Erreur lors du test des articles:\n\n${error.message}`);
    }
}

// Rendre la fonction de test accessible globalement
window.testArticlesData = testArticlesData;

// Fonction de test pour vérifier les statistiques
function testStatistiques() {
    console.log('=== TEST STATISTIQUES ===');

    console.log('Nombre de BL:', bonsLivraison.length);
    console.log('BL disponibles:', bonsLivraison);

    // Forcer la mise à jour des statistiques
    updateStats();

    // Vérifier les éléments DOM
    const elements = {
        'total-bons': totalBonsEl,
        'total-montant': totalMontantEl,
        'total-quantite': totalQuantiteEl
    };

    console.log('Éléments DOM des statistiques:');
    Object.entries(elements).forEach(([id, element]) => {
        if (element) {
            console.log(`- ${id}: "${element.textContent}"`);
        } else {
            console.log(`- ${id}: ÉLÉMENT NON TROUVÉ`);
        }
    });

    alert(`📊 Test Statistiques\n\nNombre de BL: ${bonsLivraison.length}\n\nRésultats dans la console (F12)`);
}

// Rendre la fonction de test accessible globalement
window.testStatistiques = testStatistiques;

// Fonction de test spécifique pour les statistiques de l'onglet Statistiques
function testStatistiquesOnglet() {
    console.log('=== TEST STATISTIQUES ONGLET ===');

    console.log('Nombre de BL:', bonsLivraison.length);
    console.log('Nombre d\'articles:', articles.length);

    // Forcer la mise à jour des statistiques principales
    updateMainStatistics();

    // Vérifier les éléments DOM de l'onglet Statistiques
    const elements = {
        'stats-total-bons': document.getElementById('stats-total-bons'),
        'stats-total-articles': document.getElementById('stats-total-articles'),
        'stats-montant-total': document.getElementById('stats-montant-total'),
        'stats-quantite-totale': document.getElementById('stats-quantite-totale')
    };

    console.log('Éléments DOM de l\'onglet Statistiques:');
    Object.entries(elements).forEach(([id, element]) => {
        if (element) {
            console.log(`- ${id}: "${element.textContent}"`);
        } else {
            console.log(`- ${id}: ÉLÉMENT NON TROUVÉ`);
        }
    });

    alert(`📊 Test Statistiques Onglet\n\nNombre de BL: ${bonsLivraison.length}\nNombre d'articles: ${articles.length}\n\nRésultats dans la console (F12)`);
}

// Rendre la fonction de test accessible globalement
window.testStatistiquesOnglet = testStatistiquesOnglet;

// Voir les détails d'un bon de livraison
async function viewBonDetails(id) {
    console.log('=== DEBUG VIEW BON DETAILS ===');
    console.log('ID reçu pour détails:', id, 'Type:', typeof id);

    try {
        // Vérifier que l'API Electron est disponible
        if (!window.electronAPI) {
            throw new Error('API Electron non disponible');
        }

        if (!window.electronAPI.getBonWithLignes) {
            throw new Error('Méthode getBonWithLignes non disponible dans l\'API Electron');
        }

        // Convertir l'ID si nécessaire
        const numericId = typeof id === 'string' ? parseInt(id) : id;
        console.log('ID numérique pour API:', numericId);

        if (isNaN(numericId) || numericId <= 0) {
            throw new Error(`ID invalide: ${id} -> ${numericId}`);
        }

        console.log('Appel de getBonWithLignes avec ID:', numericId);
        const bonDetails = await window.electronAPI.getBonWithLignes(numericId);
        console.log('Détails BL reçus:', bonDetails);

        if (!bonDetails) {
            console.error('Aucun détail reçu pour ID:', numericId);

            // Essayer de trouver le BL dans les données locales
            const bonLocal = bonsLivraison.find(b => b.id == numericId);
            if (bonLocal) {
                console.log('BL trouvé localement, création d\'une modal simplifiée');
                showSimpleBLDetails(bonLocal);
                return;
            }

            alert('Impossible de charger les détails de ce bon de livraison.');
            return;
        }

        let detailsHTML = `
            <div class="bon-details-modal">
                <div class="bon-details-header">
                    <h3>📋 Détails du Bon de Livraison</h3>
                    <button onclick="closeBonDetails()" class="close-btn">×</button>
                </div>

                <div class="bon-info-section">
                    <div class="bon-info-grid">
                        <div class="info-item">
                            <label>Numéro BL :</label>
                            <span><strong>${bonDetails.numeroBL}</strong></span>
                        </div>
                        <div class="info-item">
                            <label>Date :</label>
                            <span>${formatDate(bonDetails.dateBL)}</span>
                        </div>
                        <div class="info-item">
                            <label>Fournisseur :</label>
                            <span>${bonDetails.fournisseur || 'Non spécifié'}</span>
                        </div>
                        <div class="info-item">
                            <label>Montant Total :</label>
                            <span><strong>${bonDetails.montantBL.toFixed(3)} DT</strong></span>
                        </div>
                    </div>
                </div>

                <div class="articles-section">
                    <h4>💊 Articles du Bon de Livraison</h4>
                    <table class="details-table">
                        <thead>
                            <tr>
                                <th>Article</th>
                                <th>Quantité</th>
                                <th>Prix Unitaire</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        if (bonDetails.lignes && bonDetails.lignes.length > 0) {
            console.log('Génération des lignes d\'articles pour modal:', bonDetails.lignes.length);

            for (let i = 0; i < bonDetails.lignes.length; i++) {
                const ligne = bonDetails.lignes[i];
                if (!ligne) continue;

                console.log(`Ligne ${i + 1} pour modal:`, ligne);

                // Déterminer le nom de l'article avec plusieurs tentatives
                let articleNom = ligne.article_nom ||
                               ligne.designation ||
                               ligne.nom ||
                               ligne.libelle ||
                               'Article inconnu';

                console.log(`Nom article pour modal: "${articleNom}"`);

                // Sécuriser les valeurs numériques
                const quantite = ligne.quantite_livree || ligne.quantite || 0;
                const prixUnitaire = ligne.prix_unitaire || 0;
                const totalLigne = ligne.total_ligne || ligne.total || 0;

                detailsHTML += `
                    <tr>
                        <td><strong>${articleNom}</strong>${ligne.dosage ? '<br><small>' + ligne.dosage + '</small>' : ''}</td>
                        <td>${quantite}</td>
                        <td>${prixUnitaire.toFixed(3)} DT</td>
                        <td>${totalLigne.toFixed(3)} DT</td>
                    </tr>
                `;
            }
        } else {
            console.log('Aucune ligne d\'article trouvée pour ce BL');
            detailsHTML += `
                <tr>
                    <td colspan="4" style="text-align: center; color: #64748b;">
                        Aucun article dans ce bon de livraison
                    </td>
                </tr>
            `;
        }

        detailsHTML += `
                        </tbody>
                    </table>
                </div>

                <div class="details-actions">
                    <button onclick="editBon('${id}')" class="btn btn-primary">
                        ✏️ Modifier ce BL
                    </button>
                    <button onclick="closeBonDetails()" class="btn btn-secondary">
                        Fermer
                    </button>
                </div>
            </div>
        `;

        // Créer et afficher la modal
        const modal = document.createElement('div');
        modal.id = 'bon-details-modal';
        modal.className = 'modal-overlay';
        modal.innerHTML = detailsHTML;

        document.body.appendChild(modal);

        // Ajouter l'écouteur pour fermer en cliquant à l'extérieur
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeBonDetails();
            }
        });

    } catch (error) {
        console.error('=== ERREUR DETAILS BL ===');
        console.error('Erreur complète:', error);
        console.error('Message d\'erreur:', error.message);
        console.error('Stack trace:', error.stack);
        console.error('ID utilisé:', id);
        console.error('Type ID:', typeof id);

        alert(`Erreur lors du chargement des détails du bon de livraison:\n\nErreur: ${error.message}\n\nID: ${id} (${typeof id})\n\nVérifiez la console (F12) pour plus de détails.`);
    }
}

// Afficher les détails simplifiés d'un BL (fallback)
function showSimpleBLDetails(bon) {
    console.log('Affichage des détails simplifiés pour:', bon);

    const detailsHTML = `
        <div class="bon-details-modal">
            <div class="bon-details-header">
                <h3>📋 Détails du Bon de Livraison (Mode Simplifié)</h3>
                <button onclick="closeBonDetails()" class="close-btn">×</button>
            </div>

            <div class="bon-info-section">
                <div class="bon-info-grid">
                    <div class="info-item">
                        <label>Numéro BL :</label>
                        <span><strong>${bon.numeroBL || 'N/A'}</strong></span>
                    </div>
                    <div class="info-item">
                        <label>Date :</label>
                        <span>${formatDate(bon.dateBL)}</span>
                    </div>
                    <div class="info-item">
                        <label>Fournisseur :</label>
                        <span>${bon.fournisseur || 'Non spécifié'}</span>
                    </div>
                    <div class="info-item">
                        <label>Montant Total :</label>
                        <span><strong>${bon.montantBL ? bon.montantBL.toFixed(3) : '0.000'} DT</strong></span>
                    </div>
                </div>
            </div>

            <div class="articles-section">
                <h4>⚠️ Informations Limitées</h4>
                <p style="text-align: center; color: #64748b; padding: 2rem;">
                    Les détails complets des articles ne peuvent pas être chargés.<br>
                    Problème de connexion à la base de données.<br><br>
                    <strong>Informations disponibles :</strong><br>
                    Nombre de lignes: ${bon.nbLignes || 'N/A'}<br>
                    Quantité totale: ${bon.totalQuantite || 'N/A'}
                </p>
            </div>

            <div class="details-actions">
                <button onclick="editBon('${bon.id}')" class="btn btn-primary">
                    ✏️ Modifier ce BL
                </button>
                <button onclick="closeBonDetails()" class="btn btn-secondary">
                    Fermer
                </button>
            </div>
        </div>
    `;

    // Créer et afficher la modal
    const modal = document.createElement('div');
    modal.id = 'bon-details-modal';
    modal.className = 'modal-overlay';
    modal.innerHTML = detailsHTML;

    document.body.appendChild(modal);

    // Ajouter l'écouteur pour fermer en cliquant à l'extérieur
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeBonDetails();
        }
    });
}

// Fermer la modal des détails
function closeBonDetails() {
    const modal = document.getElementById('bon-details-modal');
    if (modal) {
        document.body.removeChild(modal);
    }
}

// Supprimer un bon
async function deleteBon(id) {
    console.log('Suppression du BL avec ID:', id);

    const bon = bonsLivraison.find(b => b.id === id);
    const bonNumber = bon ? bon.numeroBL : id;

    if (confirm(`Êtes-vous sûr de vouloir supprimer le bon de livraison "${bonNumber}" ?\n\nCette action est irréversible.`)) {
        try {
            console.log('Suppression confirmée pour:', bonNumber);
            await window.electronAPI.deleteBon(id);
            await loadData();
            updateDisplay();
            alert(`Bon de livraison "${bonNumber}" supprimé avec succès!`);
        } catch (error) {
            console.error('Erreur lors de la suppression:', error);
            alert('Erreur lors de la suppression du bon de livraison:\n\n' + error.message);
        }
    } else {
        console.log('Suppression annulée par l\'utilisateur');
    }
}

// Gérer la recherche
function handleSearch() {
    updateTable();
}

// Formater la date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR');
}

// Note: La sauvegarde est maintenant gérée par la base de données SQLite

// Charger les données depuis la base de données
async function loadData() {
    try {
        if (window.electronAPI) {
            const bons = await window.electronAPI.getAllBons();
            bonsLivraison = bons.map(bon => ({
                id: bon.id,
                numeroBL: bon.numero_bl,
                dateBL: bon.date_bl,
                montantBL: bon.montant_total,
                fournisseur: bon.fournisseur,
                statut: bon.statut,
                notes: bon.notes,
                nbLignes: bon.nb_lignes,
                totalQuantite: bon.total_quantite
            }));
        }
    } catch (error) {
        console.error('Erreur lors du chargement des données:', error);
        bonsLivraison = [];
    }
}

// Charger les articles depuis la base de données
async function loadArticles() {
    try {
        if (window.electronAPI) {
            articles = await window.electronAPI.getAllArticles();
        }
    } catch (error) {
        console.error('Erreur lors du chargement des articles:', error);
        articles = [];
    }
}

// Exporter les données
async function exportData() {
    try {
        if (window.electronAPI) {
            const data = await window.electronAPI.exportToCSV();

            if (data.length === 0) {
                alert('Aucune donnée à exporter.');
                return;
            }

            // Créer un CSV
            const headers = Object.keys(data[0]).join(',');
            const rows = data.map(row =>
                Object.values(row).map(value =>
                    `"${value || ''}"`
                ).join(',')
            ).join('\n');

            const csv = headers + '\n' + rows;

            // Télécharger le fichier
            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `bons_livraison_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            alert('Export réalisé avec succès!');
        } else {
            alert('Fonctionnalité d\'export disponible uniquement dans l\'application desktop.');
        }
    } catch (error) {
        console.error('Erreur lors de l\'export:', error);
        alert('Erreur lors de l\'export des données.');
    }
}

// ===== GESTION DES ONGLETS =====

// Variables pour les onglets
let currentTab = 'bons-livraison';

// Initialiser les onglets
function initializeTabs() {
    console.log('Initialisation des onglets...');

    // Ajouter les écouteurs pour les onglets
    const navTabs = document.querySelectorAll('.nav-tab');
    for (let i = 0; i < navTabs.length; i++) {
        const tab = navTabs[i];
        if (!tab) continue;

        tab.addEventListener('click', (e) => {
            e.preventDefault();
            const tabName = tab.getAttribute('data-tab');
            console.log('🖱️ Onglet cliqué:', tabName);
            switchTab(tabName);
        });
    }

    // Initialiser les écouteurs pour les nouvelles sections
    initializeArticlesSection();
    initializeStatisticsSection();
    initializeGroupageSection();
    initializeQRScannerSection();

    // Ajouter des raccourcis clavier pour tester et debugger
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 't') {
            e.preventDefault();
            testBLActions();
        }
        if (e.ctrlKey && e.key === 'd') {
            e.preventDefault();
            debugBLData();
            alert('Données BL affichées dans la console (F12)');
        }
        // Raccourci Ctrl+R supprimé (test détails BL)
        if (e.ctrlKey && e.key === 'e') {
            e.preventDefault();
            testElectronAPI();
        }
        if (e.ctrlKey && e.key === 'a') {
            e.preventDefault();
            testArticlesData();
        }
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            testStatistiques();
        }
        if (e.ctrlKey && e.key === 'q') {
            e.preventDefault();
            testStatistiquesOnglet();
        }
    });

    // Afficher l'onglet par défaut
    switchTab('bons-livraison');
}

// Changer d'onglet - VERSION OPTIMISÉE
function switchTab(tabName) {
    console.log('🔄 Changement rapide vers l\'onglet:', tabName);

    // Éviter le changement inutile
    if (currentTab === tabName) {
        console.log('Onglet déjà actif, pas de changement');
        return;
    }

    // Afficher un indicateur de chargement rapide
    showTabLoadingIndicator(tabName);

    // Désactiver temporairement les transitions pour la vitesse
    document.body.style.transition = 'none';

    try {
        // 1. Masquer IMMÉDIATEMENT toutes les sections
        const allSections = document.querySelectorAll('.tab-section');
        for (let i = 0; i < allSections.length; i++) {
            const section = allSections[i];
            if (section) {
                section.classList.remove('active');
                section.classList.add('hidden');
                section.style.display = 'none';
            }
        }

        // 2. Mettre à jour les onglets de navigation
        const navTabs = document.querySelectorAll('.nav-tab');
        for (let i = 0; i < navTabs.length; i++) {
            const tab = navTabs[i];
            if (!tab) continue;

            tab.classList.remove('active');
            if (tab.getAttribute('data-tab') === tabName) {
                tab.classList.add('active');
            }
        }

        // 3. Afficher IMMÉDIATEMENT la section active
        const activeSection = document.getElementById(tabName + '-section');
        if (activeSection) {
            activeSection.style.display = 'block';
            activeSection.classList.remove('hidden');
            activeSection.classList.add('active');
        }

        // 4. Mettre à jour la variable globale
        currentTab = tabName;

        // 5. Charger les données en arrière-plan (non bloquant)
        setTimeout(() => {
            switch (tabName) {
                case 'articles':
                    loadArticlesTab();
                    break;
                case 'statistiques':
                    loadStatisticsTab();
                    break;
                case 'groupage':
                    loadGroupageTab();
                    break;
                case 'qr-scanner':
                    loadQRScannerTab();
                    break;
                case 'bons-livraison':
                    updateDisplay();
                    break;
            }
        }, 50); // Petit délai pour permettre l'affichage immédiat

    } catch (error) {
        console.error('Erreur lors du changement d\'onglet:', error);
    } finally {
        // Réactiver les transitions après un court délai
        setTimeout(() => {
            document.body.style.transition = '';
        }, 100);
    }

    console.log('✅ Changement d\'onglet terminé vers:', tabName);

    // Masquer l'indicateur de chargement
    hideTabLoadingIndicator();
}

// Fonctions d'indicateur de chargement
function showTabLoadingIndicator(tabName) {
    // Créer ou mettre à jour l'indicateur
    let indicator = document.getElementById('tab-loading-indicator');
    if (!indicator) {
        indicator = document.createElement('div');
        indicator.id = 'tab-loading-indicator';
        indicator.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(59, 130, 246, 0.9);
            color: white;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-weight: 500;
            z-index: 9999;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;
        document.body.appendChild(indicator);
    }

    const tabLabels = {
        'bons-livraison': 'Bons de Livraison',
        'articles': 'Articles',
        'statistiques': 'Statistiques'
    };

    indicator.innerHTML = `
        <div style="
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255,255,255,0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        "></div>
        Chargement ${tabLabels[tabName] || tabName}...
    `;

    indicator.style.display = 'flex';
}

function hideTabLoadingIndicator() {
    const indicator = document.getElementById('tab-loading-indicator');
    if (indicator) {
        indicator.style.display = 'none';
    }
}

// ===== SECTION ARTICLES =====

function initializeArticlesSection() {
    console.log('Initialisation de la section Articles...');

    // Boutons de la section articles
    const btnImportArticlesTab = document.getElementById('btn-import-articles-tab');
    const btnExportArticles = document.getElementById('btn-export-articles');
    const btnAddArticle = document.getElementById('btn-add-article');
    const articlesSearch = document.getElementById('articles-search');
    const articlesFilterStock = document.getElementById('articles-filter-stock');
    const articlesSort = document.getElementById('articles-sort');

    if (btnImportArticlesTab) {
        btnImportArticlesTab.addEventListener('click', importArticles);
    }

    if (btnExportArticles) {
        btnExportArticles.addEventListener('click', exportArticles);
    }

    if (btnAddArticle) {
        btnAddArticle.addEventListener('click', showAddArticleForm);
    }

    if (articlesSearch) {
        articlesSearch.addEventListener('input', filterArticles);
    }

    if (articlesFilterStock) {
        articlesFilterStock.addEventListener('change', filterArticles);
    }

    if (articlesSort) {
        articlesSort.addEventListener('change', filterArticles);
    }
}

function loadArticlesTab() {
    console.log('Chargement rapide de l\'onglet Articles...');

    // Chargement asynchrone pour éviter le blocage
    requestAnimationFrame(() => {
        try {
            updateArticlesStats();
            displayArticles();
            console.log('✅ Onglet Articles chargé');
        } catch (error) {
            console.error('Erreur chargement Articles:', error);
        }
    });
}

function updateArticlesStats() {
    const totalArticlesCount = articles.length;
    const articlesWithStock = articles.filter(a => a.stock_actuel > 0).length;
    const averagePrice = articles.length > 0 ?
        articles.reduce((sum, a) => sum + a.prix_unitaire, 0) / articles.length : 0;
    const totalStockValue = articles.reduce((sum, a) =>
        sum + (a.prix_unitaire * a.stock_actuel), 0);

    // Mettre à jour les éléments DOM
    const totalArticlesCountEl = document.getElementById('total-articles-count');
    const articlesWithStockEl = document.getElementById('articles-with-stock');
    const averagePriceEl = document.getElementById('average-price');
    const totalStockValueEl = document.getElementById('total-stock-value');

    if (totalArticlesCountEl) totalArticlesCountEl.textContent = totalArticlesCount;
    if (articlesWithStockEl) articlesWithStockEl.textContent = articlesWithStock;
    if (averagePriceEl) averagePriceEl.textContent = averagePrice.toFixed(3) + ' DT';
    if (totalStockValueEl) totalStockValueEl.textContent = totalStockValue.toFixed(3) + ' DT';
}

function displayArticles() {
    const tableBody = document.getElementById('articles-table-body');
    const emptyState = document.getElementById('articles-empty-state');
    const tableContainer = document.querySelector('#articles-section .table-container');

    if (!tableBody) return;

    tableBody.innerHTML = '';

    if (articles.length === 0) {
        if (emptyState) emptyState.style.display = 'block';
        if (tableContainer) tableContainer.style.display = 'none';
        return;
    }

    if (emptyState) emptyState.style.display = 'none';
    if (tableContainer) tableContainer.style.display = 'block';

    const filteredArticles = getFilteredArticles();

    for (let i = 0; i < filteredArticles.length; i++) {
        const article = filteredArticles[i];
        if (!article) continue;

        const row = document.createElement('tr');
        const stockValue = (article.prix_unitaire * article.stock_actuel).toFixed(3);
        const lastUsed = getLastUsedDate(article.designation);

        row.innerHTML = `
            <td><strong>${article.designation}</strong></td>
            <td>${article.prix_unitaire.toFixed(3)} DT</td>
            <td>
                <span class="stock-badge ${article.stock_actuel > 0 ? 'in-stock' : 'out-of-stock'}">
                    ${article.stock_actuel}
                </span>
            </td>
            <td>${stockValue} DT</td>
            <td>${lastUsed || 'Jamais utilisé'}</td>
            <td>
                <div class="actions">
                    <button class="btn btn-edit btn-small" onclick="editArticle('${article.id}')">
                        ✏️ Modifier
                    </button>
                    <button class="btn btn-danger btn-small" onclick="deleteArticle('${article.id}')">
                        🗑️ Supprimer
                    </button>
                </div>
            </td>
        `;

        tableBody.appendChild(row);
    }
}

function getFilteredArticles() {
    const searchTerm = document.getElementById('articles-search')?.value.toLowerCase() || '';
    const stockFilter = document.getElementById('articles-filter-stock')?.value || 'all';
    const sortOption = document.getElementById('articles-sort')?.value || 'name-asc';

    let filtered = articles.filter(article => {
        const matchesSearch = article.designation.toLowerCase().includes(searchTerm);

        let matchesStock = true;
        if (stockFilter === 'in-stock') {
            matchesStock = article.stock_actuel > 0;
        } else if (stockFilter === 'out-of-stock') {
            matchesStock = article.stock_actuel === 0;
        }

        return matchesSearch && matchesStock;
    });

    // Tri
    filtered.sort((a, b) => {
        switch (sortOption) {
            case 'name-desc':
                return b.designation.localeCompare(a.designation);
            case 'price-asc':
                return a.prix_unitaire - b.prix_unitaire;
            case 'price-desc':
                return b.prix_unitaire - a.prix_unitaire;
            case 'stock-desc':
                return b.stock_actuel - a.stock_actuel;
            default: // name-asc
                return a.designation.localeCompare(b.designation);
        }
    });

    return filtered;
}

function filterArticles() {
    displayArticles();
}

function getLastUsedDate(articleName) {
    // Chercher dans les bons de livraison la dernière utilisation
    let lastDate = null;

    for (let i = 0; i < bonsLivraison.length; i++) {
        const bon = bonsLivraison[i];
        if (!bon || !bon.medicaments) continue;

        for (let j = 0; j < bon.medicaments.length; j++) {
            const med = bon.medicaments[j];
            if (med && med.article === articleName) {
                const bonDate = new Date(bon.dateBL);
                if (!lastDate || bonDate > lastDate) {
                    lastDate = bonDate;
                }
            }
        }
    }

    return lastDate ? formatDate(lastDate.toISOString()) : null;
}

function exportArticles() {
    try {
        if (articles.length === 0) {
            alert('Aucun article à exporter.');
            return;
        }

        // Créer les données CSV
        const headers = ['Désignation', 'Prix Unitaire (DT)', 'Stock Actuel', 'Valeur Stock (DT)', 'Dernière Utilisation'];
        const rows = [];

        for (let i = 0; i < articles.length; i++) {
            const article = articles[i];
            if (!article) continue;

            const stockValue = (article.prix_unitaire * article.stock_actuel).toFixed(3);
            const lastUsed = getLastUsedDate(article.designation) || 'Jamais utilisé';

            rows.push([
                article.designation,
                article.prix_unitaire.toFixed(3),
                article.stock_actuel,
                stockValue,
                lastUsed
            ]);
        }

        // Créer le CSV
        const csvContent = [headers, ...rows]
            .map(row => row.map(cell => `"${cell}"`).join(','))
            .join('\n');

        // Télécharger
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `articles_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        alert('Export des articles réalisé avec succès!');
    } catch (error) {
        console.error('Erreur lors de l\'export des articles:', error);
        alert('Erreur lors de l\'export des articles.');
    }
}

function showAddArticleForm() {
    alert('Fonctionnalité d\'ajout d\'article en cours de développement.\n\nUtilisez l\'import Excel pour ajouter des articles.');
}

function editArticle(id) {
    alert('Fonctionnalité de modification d\'article en cours de développement.');
}

function deleteArticle(id) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cet article ?')) {
        alert('Fonctionnalité de suppression d\'article en cours de développement.');
    }
}

// ===== SECTION STATISTIQUES =====

function initializeStatisticsSection() {
    console.log('Initialisation de la section Statistiques...');

    const btnRefreshStats = document.getElementById('btn-refresh-stats');
    const btnExportStats = document.getElementById('btn-export-stats');

    if (btnRefreshStats) {
        btnRefreshStats.addEventListener('click', () => {
            loadStatisticsTab();
            alert('Statistiques actualisées!');
        });
    }

    if (btnExportStats) {
        btnExportStats.addEventListener('click', exportStatistics);
    }
}

function loadStatisticsTab() {
    console.log('Chargement rapide de l\'onglet Statistiques...');

    // Chargement asynchrone pour éviter le blocage
    requestAnimationFrame(() => {
        try {
            updateMainStatistics();
            // Délais échelonnés pour les statistiques détaillées
            setTimeout(() => updateDetailedStatistics(), 10);
            console.log('✅ Onglet Statistiques chargé');
        } catch (error) {
            console.error('Erreur chargement Statistiques:', error);
        }
    });
}

async function updateMainStatistics() {
    console.log('=== MISE À JOUR STATISTIQUES PRINCIPALES ===');

    // Statistiques principales
    const totalBons = bonsLivraison.length;
    const totalArticles = articles.length;

    let totalMontant = 0;
    let totalQuantite = 0;

    console.log('Calcul des statistiques pour', totalBons, 'BL');

    // Calculer la quantité totale réelle en utilisant les données de la base
    for (let i = 0; i < bonsLivraison.length; i++) {
        const bon = bonsLivraison[i];
        if (!bon) continue;

        totalMontant += bon.montantBL || 0;

        try {
            // Récupérer les détails réels du BL avec ses lignes
            if (window.electronAPI && window.electronAPI.getBonWithLignes) {
                const bonDetails = await window.electronAPI.getBonWithLignes(bon.id);
                if (bonDetails && bonDetails.lignes) {
                    console.log(`BL ${bon.numeroBL}: ${bonDetails.lignes.length} lignes`);
                    for (let j = 0; j < bonDetails.lignes.length; j++) {
                        const ligne = bonDetails.lignes[j];
                        if (ligne && ligne.quantite_livree) {
                            totalQuantite += ligne.quantite_livree;
                            console.log(`  - Article: ${ligne.article_nom || 'N/A'}, Quantité: ${ligne.quantite_livree}`);
                        }
                    }
                }
            } else {
                // Fallback : utiliser les données locales si l'API n'est pas disponible
                console.log('API non disponible, utilisation des données locales pour BL', bon.numeroBL);
                if (bon.medicaments) {
                    for (let j = 0; j < bon.medicaments.length; j++) {
                        const med = bon.medicaments[j];
                        if (med && med.quantite) {
                            totalQuantite += med.quantite;
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Erreur lors du calcul des quantités pour BL', bon.id, ':', error);
            // Fallback en cas d'erreur
            if (bon.medicaments) {
                for (let j = 0; j < bon.medicaments.length; j++) {
                    const med = bon.medicaments[j];
                    if (med && med.quantite) {
                        totalQuantite += med.quantite;
                    }
                }
            }
        }
    }

    console.log('Statistiques principales calculées:');
    console.log('- Total BL:', totalBons);
    console.log('- Total Articles:', totalArticles);
    console.log('- Montant total:', totalMontant.toFixed(3), 'DT');
    console.log('- Quantité totale réelle:', totalQuantite);

    // Mettre à jour les éléments DOM
    const statsTotalBons = document.getElementById('stats-total-bons');
    const statsTotalArticles = document.getElementById('stats-total-articles');
    const statsMontantTotal = document.getElementById('stats-montant-total');
    const statsQuantiteTotale = document.getElementById('stats-quantite-totale');

    if (statsTotalBons) {
        statsTotalBons.textContent = totalBons;
        console.log('Mis à jour stats-total-bons:', totalBons);
    }
    if (statsTotalArticles) {
        statsTotalArticles.textContent = totalArticles;
        console.log('Mis à jour stats-total-articles:', totalArticles);
    }
    if (statsMontantTotal) {
        statsMontantTotal.textContent = totalMontant.toFixed(3) + ' DT';
        console.log('Mis à jour stats-montant-total:', totalMontant.toFixed(3), 'DT');
    }
    if (statsQuantiteTotale) {
        statsQuantiteTotale.textContent = totalQuantite;
        console.log('Mis à jour stats-quantite-totale:', totalQuantite);
    } else {
        console.error('Élément stats-quantite-totale non trouvé !');
    }
}

function updateDetailedStatistics() {
    updateMonthlyStats();
    updateTopArticles();
    updateSuppliersStats();
    updateTrends();
}

function updateMonthlyStats() {
    const now = new Date();
    const thisMonth = now.getMonth();
    const thisYear = now.getFullYear();
    const lastMonth = thisMonth === 0 ? 11 : thisMonth - 1;
    const lastMonthYear = thisMonth === 0 ? thisYear - 1 : thisYear;

    let thisMonthCount = 0;
    let lastMonthCount = 0;

    for (let i = 0; i < bonsLivraison.length; i++) {
        const bon = bonsLivraison[i];
        if (!bon) continue;

        const bonDate = new Date(bon.dateBL);
        const bonMonth = bonDate.getMonth();
        const bonYear = bonDate.getFullYear();

        if (bonMonth === thisMonth && bonYear === thisYear) {
            thisMonthCount++;
        } else if (bonMonth === lastMonth && bonYear === lastMonthYear) {
            lastMonthCount++;
        }
    }

    const avgMonth = bonsLivraison.length > 0 ?
        Math.round(bonsLivraison.length / Math.max(1, getMonthsSinceFirstBL())) : 0;

    const statsThisMonth = document.getElementById('stats-this-month');
    const statsLastMonth = document.getElementById('stats-last-month');
    const statsAvgMonth = document.getElementById('stats-avg-month');

    if (statsThisMonth) statsThisMonth.textContent = thisMonthCount + ' BL';
    if (statsLastMonth) statsLastMonth.textContent = lastMonthCount + ' BL';
    if (statsAvgMonth) statsAvgMonth.textContent = avgMonth + ' BL';
}

function getMonthsSinceFirstBL() {
    if (bonsLivraison.length === 0) return 1;

    const dates = bonsLivraison.map(bon => new Date(bon.dateBL)).sort((a, b) => a - b);
    const firstDate = dates[0];
    const now = new Date();

    const yearDiff = now.getFullYear() - firstDate.getFullYear();
    const monthDiff = now.getMonth() - firstDate.getMonth();

    return Math.max(1, yearDiff * 12 + monthDiff + 1);
}

function updateTopArticles() {
    const articleStats = {};

    // Compter les quantités par article
    for (let i = 0; i < bonsLivraison.length; i++) {
        const bon = bonsLivraison[i];
        if (!bon || !bon.medicaments) continue;

        for (let j = 0; j < bon.medicaments.length; j++) {
            const med = bon.medicaments[j];
            if (!med) continue;

            if (!articleStats[med.article]) {
                articleStats[med.article] = {
                    name: med.article,
                    quantity: 0,
                    totalValue: 0
                };
            }

            articleStats[med.article].quantity += med.quantite || 0;
            articleStats[med.article].totalValue += med.total || 0;
        }
    }

    // Trier par quantité
    const topArticles = Object.values(articleStats)
        .sort((a, b) => b.quantity - a.quantity)
        .slice(0, 5);

    const topArticlesList = document.getElementById('top-articles-list');
    if (topArticlesList) {
        topArticlesList.innerHTML = '';

        if (topArticles.length === 0) {
            topArticlesList.innerHTML = '<p>Aucun article trouvé</p>';
            return;
        }

        for (let i = 0; i < topArticles.length; i++) {
            const article = topArticles[i];
            if (!article) continue;

            const div = document.createElement('div');
            div.className = 'top-item';
            div.innerHTML = `
                <div class="top-item-name">${article.name}</div>
                <div class="top-item-value">${article.quantity} unités</div>
            `;
            topArticlesList.appendChild(div);
        }
    }
}

function updateSuppliersStats() {
    const supplierStats = {};

    for (let i = 0; i < bonsLivraison.length; i++) {
        const bon = bonsLivraison[i];
        if (!bon) continue;

        const supplier = bon.fournisseur || 'Non spécifié';

        if (!supplierStats[supplier]) {
            supplierStats[supplier] = {
                name: supplier,
                count: 0,
                totalValue: 0
            };
        }

        supplierStats[supplier].count++;
        supplierStats[supplier].totalValue += bon.montantBL || 0;
    }

    const topSuppliers = Object.values(supplierStats)
        .sort((a, b) => b.totalValue - a.totalValue)
        .slice(0, 5);

    const suppliersStats = document.getElementById('suppliers-stats');
    if (suppliersStats) {
        suppliersStats.innerHTML = '';

        if (topSuppliers.length === 0) {
            suppliersStats.innerHTML = '<p>Aucun fournisseur trouvé</p>';
            return;
        }

        for (let i = 0; i < topSuppliers.length; i++) {
            const supplier = topSuppliers[i];
            if (!supplier) continue;

            const div = document.createElement('div');
            div.className = 'supplier-item';
            div.innerHTML = `
                <div class="supplier-name">${supplier.name}</div>
                <div class="supplier-stats">
                    ${supplier.count} BL<br>
                    ${supplier.totalValue.toFixed(3)} DT
                </div>
            `;
            suppliersStats.appendChild(div);
        }
    }
}

function updateTrends() {
    // Calcul de la croissance mensuelle
    const now = new Date();
    const thisMonth = now.getMonth();
    const thisYear = now.getFullYear();
    const lastMonth = thisMonth === 0 ? 11 : thisMonth - 1;
    const lastMonthYear = thisMonth === 0 ? thisYear - 1 : thisYear;

    let thisMonthValue = 0;
    let lastMonthValue = 0;

    for (let i = 0; i < bonsLivraison.length; i++) {
        const bon = bonsLivraison[i];
        if (!bon) continue;

        const bonDate = new Date(bon.dateBL);
        const bonMonth = bonDate.getMonth();
        const bonYear = bonDate.getFullYear();

        if (bonMonth === thisMonth && bonYear === thisYear) {
            thisMonthValue += bon.montantBL || 0;
        } else if (bonMonth === lastMonth && bonYear === lastMonthYear) {
            lastMonthValue += bon.montantBL || 0;
        }
    }

    const growthRate = lastMonthValue > 0 ?
        ((thisMonthValue - lastMonthValue) / lastMonthValue * 100).toFixed(1) : 0;

    // Article le plus commandé
    const articleStats = {};
    for (let i = 0; i < bonsLivraison.length; i++) {
        const bon = bonsLivraison[i];
        if (!bon || !bon.medicaments) continue;

        for (let j = 0; j < bon.medicaments.length; j++) {
            const med = bon.medicaments[j];
            if (!med) continue;

            articleStats[med.article] = (articleStats[med.article] || 0) + (med.quantite || 0);
        }
    }

    const mostOrdered = Object.keys(articleStats).length > 0 ?
        Object.keys(articleStats).reduce((a, b) => articleStats[a] > articleStats[b] ? a : b) : '-';

    // Valeur moyenne par BL
    const avgBLValue = bonsLivraison.length > 0 ?
        bonsLivraison.reduce((sum, bon) => sum + (bon.montantBL || 0), 0) / bonsLivraison.length : 0;

    // Mettre à jour les éléments DOM
    const growthRateEl = document.getElementById('growth-rate');
    const mostOrderedEl = document.getElementById('most-ordered');
    const avgBLValueEl = document.getElementById('avg-bl-value');

    if (growthRateEl) {
        growthRateEl.textContent = (growthRate >= 0 ? '+' : '') + growthRate + '%';
        growthRateEl.style.color = growthRate >= 0 ? '#10b981' : '#ef4444';
    }
    if (mostOrderedEl) mostOrderedEl.textContent = mostOrdered;
    if (avgBLValueEl) avgBLValueEl.textContent = avgBLValue.toFixed(3) + ' DT';
}

function exportStatistics() {
    try {
        // Créer un rapport de statistiques
        const report = {
            date_generation: new Date().toISOString(),
            statistiques_principales: {
                total_bons: bonsLivraison.length,
                total_articles: articles.length,
                montant_total: bonsLivraison.reduce((sum, bon) => sum + (bon.montantBL || 0), 0),
                quantite_totale: bonsLivraison.reduce((sum, bon) => {
                    if (!bon.medicaments) return sum;
                    return sum + bon.medicaments.reduce((medSum, med) => medSum + (med.quantite || 0), 0);
                }, 0)
            }
        };

        // Convertir en JSON formaté
        const jsonContent = JSON.stringify(report, null, 2);

        // Télécharger
        const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `statistiques_${new Date().toISOString().split('T')[0]}.json`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        alert('Rapport de statistiques exporté avec succès!');
    } catch (error) {
        console.error('Erreur lors de l\'export des statistiques:', error);
        alert('Erreur lors de l\'export des statistiques.');
    }
}

// ===== MODULE DE GROUPAGE BL =====

// Variables pour le groupage
let selectedBLs = [];
let groupageData = {
    selectedBLs: [],
    groupedArticles: {},
    totalAmount: 0,
    totalQuantity: 0
};

function initializeGroupageSection() {
    console.log('Initialisation de la section Groupage...');

    // Boutons principaux
    const btnResetGroupage = document.getElementById('btn-reset-groupage');
    const btnCreateGroupedBL = document.getElementById('btn-create-grouped-bl');

    // Boutons de sélection
    const btnSelectAllBL = document.getElementById('btn-select-all-bl');
    const btnDeselectAllBL = document.getElementById('btn-deselect-all-bl');
    const selectAllCheckbox = document.getElementById('select-all-checkbox');

    // Filtres
    const groupageFilterSupplier = document.getElementById('groupage-filter-supplier');
    const groupageFilterPeriod = document.getElementById('groupage-filter-period');
    const groupageDateFrom = document.getElementById('groupage-date-from');
    const groupageDateTo = document.getElementById('groupage-date-to');

    // Configuration
    const groupedBLDate = document.getElementById('grouped-bl-date');

    // Écouteurs d'événements
    if (btnResetGroupage) {
        btnResetGroupage.addEventListener('click', resetGroupage);
    }

    if (btnCreateGroupedBL) {
        btnCreateGroupedBL.addEventListener('click', createGroupedBL);
    }

    if (btnSelectAllBL) {
        btnSelectAllBL.addEventListener('click', selectAllBLs);
    }

    if (btnDeselectAllBL) {
        btnDeselectAllBL.addEventListener('click', deselectAllBLs);
    }

    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', toggleAllBLSelection);
    }

    if (groupageFilterSupplier) {
        groupageFilterSupplier.addEventListener('change', filterBLs);
    }

    if (groupageFilterPeriod) {
        groupageFilterPeriod.addEventListener('change', handlePeriodFilter);
    }

    if (groupageDateFrom) {
        groupageDateFrom.addEventListener('change', filterBLs);
    }

    if (groupageDateTo) {
        groupageDateTo.addEventListener('change', filterBLs);
    }

    // Définir la date d'aujourd'hui par défaut
    if (groupedBLDate) {
        groupedBLDate.value = new Date().toISOString().split('T')[0];
    }
}

function loadGroupageTab() {
    console.log('Chargement rapide de l\'onglet Groupage...');

    requestAnimationFrame(() => {
        try {
            resetGroupage();
            loadAvailableBLs();
            updateSupplierFilter();
            generateGroupedBLNumber();
            console.log('✅ Onglet Groupage chargé');
        } catch (error) {
            console.error('Erreur chargement Groupage:', error);
        }
    });
}

function loadAvailableBLs() {
    console.log('Chargement des BL disponibles pour groupage...');

    const tableBody = document.getElementById('bl-selection-table-body');
    const emptyState = document.getElementById('groupage-empty-state');
    const tableContainer = document.querySelector('#groupage-section .table-container');
    const totalBLCount = document.getElementById('total-bl-count');

    if (!tableBody) return;

    tableBody.innerHTML = '';

    if (bonsLivraison.length === 0) {
        if (emptyState) emptyState.style.display = 'block';
        if (tableContainer) tableContainer.style.display = 'none';
        return;
    }

    if (emptyState) emptyState.style.display = 'none';
    if (tableContainer) tableContainer.style.display = 'block';

    const filteredBLs = getFilteredBLs();

    if (totalBLCount) totalBLCount.textContent = filteredBLs.length;

    for (let i = 0; i < filteredBLs.length; i++) {
        const bl = filteredBLs[i];
        if (!bl) continue;

        const row = document.createElement('tr');
        const articlesCount = bl.medicaments ? bl.medicaments.length : 0;
        const totalQuantity = bl.medicaments ?
            bl.medicaments.reduce((sum, med) => sum + (med.quantite || 0), 0) : 0;

        row.innerHTML = `
            <td>
                <input type="checkbox" class="bl-checkbox" data-bl-id="${bl.id}" onchange="toggleBLSelection('${bl.id}')">
            </td>
            <td><strong>${bl.numeroBL}</strong></td>
            <td>${formatDate(bl.dateBL)}</td>
            <td>${bl.fournisseur || 'Non spécifié'}</td>
            <td>${bl.montantBL.toFixed(3)} DT</td>
            <td>${articlesCount} article(s)</td>
            <td>${totalQuantity} unité(s)</td>
        `;

        tableBody.appendChild(row);
    }

    updateSelectionStats();
}

function getFilteredBLs() {
    const supplierFilter = document.getElementById('groupage-filter-supplier')?.value || 'all';
    const periodFilter = document.getElementById('groupage-filter-period')?.value || 'all';
    const dateFrom = document.getElementById('groupage-date-from')?.value;
    const dateTo = document.getElementById('groupage-date-to')?.value;

    return bonsLivraison.filter(bl => {
        // Filtre par fournisseur
        if (supplierFilter !== 'all' && bl.fournisseur !== supplierFilter) {
            return false;
        }

        // Filtre par période
        if (periodFilter !== 'all') {
            const blDate = new Date(bl.dateBL);
            const today = new Date();

            switch (periodFilter) {
                case 'today':
                    if (blDate.toDateString() !== today.toDateString()) return false;
                    break;
                case 'week':
                    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
                    if (blDate < weekAgo) return false;
                    break;
                case 'month':
                    if (blDate.getMonth() !== today.getMonth() || blDate.getFullYear() !== today.getFullYear()) return false;
                    break;
                case 'custom':
                    if (dateFrom && blDate < new Date(dateFrom)) return false;
                    if (dateTo && blDate > new Date(dateTo)) return false;
                    break;
            }
        }

        return true;
    });
}

function updateSupplierFilter() {
    const supplierFilter = document.getElementById('groupage-filter-supplier');
    if (!supplierFilter) return;

    // Récupérer tous les fournisseurs uniques
    const suppliers = new Set();
    for (let i = 0; i < bonsLivraison.length; i++) {
        const bl = bonsLivraison[i];
        if (bl && bl.fournisseur) {
            suppliers.add(bl.fournisseur);
        }
    }

    // Vider et remplir le select
    supplierFilter.innerHTML = '<option value="all">Tous les fournisseurs</option>';

    suppliers.forEach(supplier => {
        const option = document.createElement('option');
        option.value = supplier;
        option.textContent = supplier;
        supplierFilter.appendChild(option);
    });

    // Mettre à jour aussi le select de configuration
    const groupedBLSupplier = document.getElementById('grouped-bl-supplier');
    if (groupedBLSupplier) {
        // Garder les options existantes et ajouter les fournisseurs
        suppliers.forEach(supplier => {
            const option = document.createElement('option');
            option.value = supplier;
            option.textContent = supplier;
            groupedBLSupplier.appendChild(option);
        });
    }
}

function handlePeriodFilter() {
    const periodFilter = document.getElementById('groupage-filter-period')?.value;
    const customDateRange = document.getElementById('custom-date-range');

    if (customDateRange) {
        if (periodFilter === 'custom') {
            customDateRange.style.display = 'flex';
        } else {
            customDateRange.style.display = 'none';
        }
    }

    filterBLs();
}

function filterBLs() {
    loadAvailableBLs();
}

function toggleBLSelection(blId) {
    const checkbox = document.querySelector(`input[data-bl-id="${blId}"]`);
    if (!checkbox) return;

    if (checkbox.checked) {
        if (!selectedBLs.includes(blId)) {
            selectedBLs.push(blId);
        }
    } else {
        const index = selectedBLs.indexOf(blId);
        if (index > -1) {
            selectedBLs.splice(index, 1);
        }
    }

    updateSelectionStats();
    updateGroupagePreview();
    updateSelectAllCheckbox();
}

function selectAllBLs() {
    const checkboxes = document.querySelectorAll('.bl-checkbox');
    for (let i = 0; i < checkboxes.length; i++) {
        const checkbox = checkboxes[i];
        if (checkbox && !checkbox.checked) {
            checkbox.checked = true;
            const blId = checkbox.getAttribute('data-bl-id');
            if (blId && !selectedBLs.includes(blId)) {
                selectedBLs.push(blId);
            }
        }
    }

    updateSelectionStats();
    updateGroupagePreview();
    updateSelectAllCheckbox();
}

function deselectAllBLs() {
    const checkboxes = document.querySelectorAll('.bl-checkbox');
    for (let i = 0; i < checkboxes.length; i++) {
        const checkbox = checkboxes[i];
        if (checkbox && checkbox.checked) {
            checkbox.checked = false;
        }
    }

    selectedBLs = [];
    updateSelectionStats();
    updateGroupagePreview();
    updateSelectAllCheckbox();
}

function toggleAllBLSelection() {
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    if (!selectAllCheckbox) return;

    if (selectAllCheckbox.checked) {
        selectAllBLs();
    } else {
        deselectAllBLs();
    }
}

function updateSelectAllCheckbox() {
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    const checkboxes = document.querySelectorAll('.bl-checkbox');

    if (!selectAllCheckbox || checkboxes.length === 0) return;

    const checkedCount = document.querySelectorAll('.bl-checkbox:checked').length;

    if (checkedCount === 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    } else if (checkedCount === checkboxes.length) {
        selectAllCheckbox.checked = true;
        selectAllCheckbox.indeterminate = false;
    } else {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = true;
    }
}

function updateSelectionStats() {
    const selectedBLCount = document.getElementById('selected-bl-count');
    const selectedTotalAmount = document.getElementById('selected-total-amount');

    let totalAmount = 0;

    for (let i = 0; i < selectedBLs.length; i++) {
        const blId = selectedBLs[i];
        const bl = bonsLivraison.find(b => b.id === blId);
        if (bl) {
            totalAmount += bl.montantBL || 0;
        }
    }

    if (selectedBLCount) selectedBLCount.textContent = selectedBLs.length;
    if (selectedTotalAmount) selectedTotalAmount.textContent = totalAmount.toFixed(3) + ' DT';
}

function updateGroupagePreview() {
    // Calculer les données groupées
    const groupedData = calculateGroupedData();

    // Mettre à jour les cartes de résumé
    updateSummaryCards(groupedData);

    // Mettre à jour le tableau des articles groupés
    updateGroupedArticlesTable(groupedData);

    // Mettre à jour la liste des BL sources
    updateSourceBLList();
}

function calculateGroupedData() {
    const groupedArticles = {};
    let totalAmount = 0;
    let totalQuantity = 0;

    for (let i = 0; i < selectedBLs.length; i++) {
        const blId = selectedBLs[i];
        const bl = bonsLivraison.find(b => b.id === blId);

        if (!bl || !bl.medicaments) continue;

        totalAmount += bl.montantBL || 0;

        for (let j = 0; j < bl.medicaments.length; j++) {
            const med = bl.medicaments[j];
            if (!med) continue;

            const articleName = med.article;

            if (!groupedArticles[articleName]) {
                groupedArticles[articleName] = {
                    name: articleName,
                    totalQuantity: 0,
                    totalAmount: 0,
                    prices: [],
                    presentIn: []
                };
            }

            groupedArticles[articleName].totalQuantity += med.quantite || 0;
            groupedArticles[articleName].totalAmount += med.total || 0;
            groupedArticles[articleName].prices.push(med.prixUnitaire || 0);

            if (!groupedArticles[articleName].presentIn.includes(bl.numeroBL)) {
                groupedArticles[articleName].presentIn.push(bl.numeroBL);
            }

            totalQuantity += med.quantite || 0;
        }
    }

    // Calculer les prix moyens
    Object.keys(groupedArticles).forEach(articleName => {
        const article = groupedArticles[articleName];
        const avgPrice = article.prices.length > 0 ?
            article.prices.reduce((sum, price) => sum + price, 0) / article.prices.length : 0;
        article.averagePrice = avgPrice;
    });

    return {
        groupedArticles,
        totalAmount,
        totalQuantity,
        uniqueArticlesCount: Object.keys(groupedArticles).length,
        selectedBLsCount: selectedBLs.length
    };
}

function updateSummaryCards(groupedData) {
    const summaryBLCount = document.getElementById('summary-bl-count');
    const summaryArticlesCount = document.getElementById('summary-articles-count');
    const summaryTotalQuantity = document.getElementById('summary-total-quantity');
    const summaryTotalAmount = document.getElementById('summary-total-amount');

    if (summaryBLCount) summaryBLCount.textContent = groupedData.selectedBLsCount;
    if (summaryArticlesCount) summaryArticlesCount.textContent = groupedData.uniqueArticlesCount;
    if (summaryTotalQuantity) summaryTotalQuantity.textContent = groupedData.totalQuantity;
    if (summaryTotalAmount) summaryTotalAmount.textContent = groupedData.totalAmount.toFixed(3) + ' DT';
}

function updateGroupedArticlesTable(groupedData) {
    const tableBody = document.getElementById('grouped-articles-table-body');
    if (!tableBody) return;

    tableBody.innerHTML = '';

    const articles = Object.values(groupedData.groupedArticles);

    for (let i = 0; i < articles.length; i++) {
        const article = articles[i];
        if (!article) continue;

        const row = document.createElement('tr');
        row.innerHTML = `
            <td><strong>${article.name}</strong></td>
            <td>${article.totalQuantity}</td>
            <td>${article.averagePrice.toFixed(3)} DT</td>
            <td>${article.totalAmount.toFixed(3)} DT</td>
            <td>${article.presentIn.length} BL (${article.presentIn.join(', ')})</td>
        `;

        tableBody.appendChild(row);
    }
}

function updateSourceBLList() {
    const container = document.getElementById('source-bl-details');
    if (!container) return;

    container.innerHTML = '';

    for (let i = 0; i < selectedBLs.length; i++) {
        const blId = selectedBLs[i];
        const bl = bonsLivraison.find(b => b.id === blId);

        if (!bl) continue;

        const div = document.createElement('div');
        div.className = 'source-bl-item';

        const articlesCount = bl.medicaments ? bl.medicaments.length : 0;
        const totalQuantity = bl.medicaments ?
            bl.medicaments.reduce((sum, med) => sum + (med.quantite || 0), 0) : 0;

        div.innerHTML = `
            <div class="source-bl-header">${bl.numeroBL}</div>
            <div class="source-bl-details">
                Date: ${formatDate(bl.dateBL)}<br>
                Fournisseur: ${bl.fournisseur || 'Non spécifié'}<br>
                Montant: ${bl.montantBL.toFixed(3)} DT<br>
                Articles: ${articlesCount} | Quantité: ${totalQuantity}
            </div>
        `;

        container.appendChild(div);
    }
}

function generateGroupedBLNumber() {
    const groupedBLNumber = document.getElementById('grouped-bl-number');
    if (!groupedBLNumber) return;

    const today = new Date();
    const dateStr = today.toISOString().split('T')[0].replace(/-/g, '');
    const timeStr = today.getHours().toString().padStart(2, '0') + today.getMinutes().toString().padStart(2, '0');

    groupedBLNumber.value = `BL-GROUP-${dateStr}-${timeStr}`;
}

function resetGroupage() {
    console.log('Réinitialisation du groupage...');

    // Réinitialiser les variables
    selectedBLs = [];
    groupageData = {
        selectedBLs: [],
        groupedArticles: {},
        totalAmount: 0,
        totalQuantity: 0
    };

    // Décocher toutes les cases
    const checkboxes = document.querySelectorAll('.bl-checkbox');
    for (let i = 0; i < checkboxes.length; i++) {
        const checkbox = checkboxes[i];
        if (checkbox) checkbox.checked = false;
    }

    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    if (selectAllCheckbox) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    }

    // Réinitialiser les filtres
    const groupageFilterSupplier = document.getElementById('groupage-filter-supplier');
    const groupageFilterPeriod = document.getElementById('groupage-filter-period');
    const customDateRange = document.getElementById('custom-date-range');

    if (groupageFilterSupplier) groupageFilterSupplier.value = 'all';
    if (groupageFilterPeriod) groupageFilterPeriod.value = 'all';
    if (customDateRange) customDateRange.style.display = 'none';

    // Réinitialiser la configuration
    generateGroupedBLNumber();
    const groupedBLSupplier = document.getElementById('grouped-bl-supplier');
    const groupedBLNotes = document.getElementById('grouped-bl-notes');

    if (groupedBLSupplier) groupedBLSupplier.value = '';
    if (groupedBLNotes) groupedBLNotes.value = '';

    // Mettre à jour l'affichage
    updateSelectionStats();
    updateGroupagePreview();
    loadAvailableBLs();

    alert('Groupage réinitialisé avec succès!');
}

async function createGroupedBL() {
    console.log('Création du BL groupé...');

    // Validation
    if (selectedBLs.length === 0) {
        alert('Veuillez sélectionner au moins un bon de livraison à grouper.');
        return;
    }

    const groupedBLNumber = document.getElementById('grouped-bl-number')?.value.trim();
    const groupedBLDate = document.getElementById('grouped-bl-date')?.value;
    const groupedBLSupplier = document.getElementById('grouped-bl-supplier')?.value;
    const groupedBLNotes = document.getElementById('grouped-bl-notes')?.value.trim();

    if (!groupedBLNumber || !groupedBLDate) {
        alert('Veuillez remplir le numéro et la date du BL groupé.');
        return;
    }

    // Vérifier que le numéro n'existe pas déjà
    const existingBL = bonsLivraison.find(bl => bl.numeroBL === groupedBLNumber);
    if (existingBL) {
        alert('Ce numéro de BL existe déjà. Veuillez en choisir un autre.');
        return;
    }

    try {
        // Calculer les données groupées
        const groupedData = calculateGroupedData();

        // Créer le BL groupé
        const bonData = {
            numero_bl: groupedBLNumber,
            date_bl: groupedBLDate,
            fournisseur: groupedBLSupplier || 'Groupage Multiple',
            montant_total: groupedData.totalAmount,
            statut: 'Groupé',
            notes: groupedBLNotes + `\n\nBL groupé créé à partir de: ${selectedBLs.map(id => {
                const bl = bonsLivraison.find(b => b.id === id);
                return bl ? bl.numeroBL : id;
            }).join(', ')}`
        };

        console.log('Création du bon groupé:', bonData);
        const result = await window.electronAPI.addBon(bonData);
        const bonId = result.lastInsertRowid;

        console.log('Bon groupé créé avec ID:', bonId);

        // Ajouter les lignes groupées
        const articles = Object.values(groupedData.groupedArticles);

        for (let i = 0; i < articles.length; i++) {
            const article = articles[i];
            if (!article) continue;

            // Trouver l'article dans la base
            const articleDB = articles.find(a => a.designation === article.name);

            if (articleDB) {
                const ligne = {
                    bon_id: bonId,
                    article_id: articleDB.id,
                    quantite_commandee: article.totalQuantity,
                    quantite_livree: article.totalQuantity,
                    prix_unitaire: article.averagePrice,
                    total_ligne: article.totalAmount
                };

                await window.electronAPI.addLigneLivraison(ligne);
                console.log('Ligne groupée ajoutée pour:', article.name);
            }
        }

        // Recharger les données
        await loadData();
        updateDisplay();

        // Réinitialiser le groupage
        resetGroupage();

        // Retourner à l'onglet des BL
        switchTab('bons-livraison');

        alert(`BL groupé "${groupedBLNumber}" créé avec succès!\n\n` +
              `${groupedData.selectedBLsCount} BL groupés\n` +
              `${groupedData.uniqueArticlesCount} articles uniques\n` +
              `Montant total: ${groupedData.totalAmount.toFixed(3)} DT`);

    } catch (error) {
        console.error('Erreur lors de la création du BL groupé:', error);
        alert('Erreur lors de la création du BL groupé:\n\n' + error.message);
    }
}

// ===== MODULE QR SCANNER =====

// Variables pour le scanner QR
let qrScanner = {
    isActive: false,
    stream: null,
    video: null,
    canvas: null,
    context: null,
    scanInterval: null,
    scanHistory: [],
    currentBLData: null
};

function initializeQRScannerSection() {
    console.log('Initialisation de la section QR Scanner...');

    // Éléments du DOM
    const btnToggleCamera = document.getElementById('btn-toggle-camera');
    const btnUploadQRImage = document.getElementById('btn-upload-qr-image');
    const btnGenerateDemoQR = document.getElementById('btn-generate-demo-qr');
    const btnSwitchCamera = document.getElementById('btn-switch-camera');
    const btnTestScan = document.getElementById('btn-test-scan');
    const cameraSelect = document.getElementById('camera-select');
    const qrFileInput = document.getElementById('qr-file-input');
    const uploadZone = document.getElementById('qr-upload-zone');

    // Écouteurs d'événements
    if (btnToggleCamera) {
        btnToggleCamera.addEventListener('click', toggleCamera);
    }

    if (btnUploadQRImage) {
        btnUploadQRImage.addEventListener('click', () => {
            if (qrFileInput) qrFileInput.click();
        });
    }

    if (btnGenerateDemoQR) {
        btnGenerateDemoQR.addEventListener('click', toggleDemoQRSection);
    }

    if (btnSwitchCamera) {
        btnSwitchCamera.addEventListener('click', switchCamera);
    }

    if (btnTestScan) {
        btnTestScan.addEventListener('click', testQRScan);
    }

    if (cameraSelect) {
        cameraSelect.addEventListener('change', switchToSelectedCamera);
    }

    if (qrFileInput) {
        qrFileInput.addEventListener('change', handleFileUpload);
    }

    // Drag & Drop pour l'upload
    if (uploadZone) {
        uploadZone.addEventListener('dragover', handleDragOver);
        uploadZone.addEventListener('dragleave', handleDragLeave);
        uploadZone.addEventListener('drop', handleDrop);
        uploadZone.addEventListener('click', () => {
            if (qrFileInput) qrFileInput.click();
        });
    }

    // Initialiser les éléments vidéo
    qrScanner.video = document.getElementById('qr-video');
    qrScanner.canvas = document.getElementById('qr-canvas');
    if (qrScanner.canvas) {
        qrScanner.context = qrScanner.canvas.getContext('2d');
    }

    // Charger les caméras disponibles
    loadAvailableCameras();

    // Initialiser la date démo
    const demoBLDate = document.getElementById('demo-bl-date');
    if (demoBLDate) {
        demoBLDate.value = new Date().toISOString().split('T')[0];
    }
}

function loadQRScannerTab() {
    console.log('Chargement rapide de l\'onglet QR Scanner...');

    requestAnimationFrame(() => {
        try {
            resetQRScanner();
            updateScanStats();
            console.log('✅ Onglet QR Scanner chargé');
        } catch (error) {
            console.error('Erreur chargement QR Scanner:', error);
        }
    });
}

async function loadAvailableCameras() {
    try {
        const devices = await navigator.mediaDevices.enumerateDevices();
        const videoDevices = devices.filter(device => device.kind === 'videoinput');

        const cameraSelect = document.getElementById('camera-select');
        if (!cameraSelect) return;

        cameraSelect.innerHTML = '<option value="">Sélectionner une caméra...</option>';

        for (let i = 0; i < videoDevices.length; i++) {
            const device = videoDevices[i];
            const option = document.createElement('option');
            option.value = device.deviceId;
            option.textContent = device.label || `Caméra ${i + 1}`;
            cameraSelect.appendChild(option);
        }

        console.log(`${videoDevices.length} caméra(s) détectée(s)`);
    } catch (error) {
        console.error('Erreur lors du chargement des caméras:', error);
    }
}

async function toggleCamera() {
    if (qrScanner.isActive) {
        stopCamera();
    } else {
        await startCamera();
    }
}

async function startCamera() {
    try {
        const cameraSelect = document.getElementById('camera-select');
        const deviceId = cameraSelect ? cameraSelect.value : null;

        const constraints = {
            video: {
                width: { ideal: 640 },
                height: { ideal: 480 },
                facingMode: 'environment'
            }
        };

        if (deviceId) {
            constraints.video.deviceId = { exact: deviceId };
        }

        qrScanner.stream = await navigator.mediaDevices.getUserMedia(constraints);

        if (qrScanner.video) {
            qrScanner.video.srcObject = qrScanner.stream;
            await qrScanner.video.play();
        }

        qrScanner.isActive = true;
        updateCameraStatus(true);
        startScanning();

        const btnToggleCamera = document.getElementById('btn-toggle-camera');
        if (btnToggleCamera) {
            btnToggleCamera.textContent = '📹 Arrêter Caméra';
            btnToggleCamera.classList.remove('btn-primary');
            btnToggleCamera.classList.add('btn-danger');
        }

        console.log('Caméra démarrée avec succès');

    } catch (error) {
        console.error('Erreur lors du démarrage de la caméra:', error);
        alert('Impossible d\'accéder à la caméra. Vérifiez les permissions.');
    }
}

function stopCamera() {
    if (qrScanner.stream) {
        const tracks = qrScanner.stream.getTracks();
        for (let i = 0; i < tracks.length; i++) {
            tracks[i].stop();
        }
        qrScanner.stream = null;
    }

    if (qrScanner.video) {
        qrScanner.video.srcObject = null;
    }

    if (qrScanner.scanInterval) {
        clearInterval(qrScanner.scanInterval);
        qrScanner.scanInterval = null;
    }

    qrScanner.isActive = false;
    updateCameraStatus(false);

    const btnToggleCamera = document.getElementById('btn-toggle-camera');
    if (btnToggleCamera) {
        btnToggleCamera.textContent = '📹 Démarrer Caméra';
        btnToggleCamera.classList.remove('btn-danger');
        btnToggleCamera.classList.add('btn-primary');
    }

    console.log('Caméra arrêtée');
}

function updateCameraStatus(isActive) {
    const statusDot = document.querySelector('.status-dot');
    const statusText = document.querySelector('.status-text');

    if (statusDot) {
        if (isActive) {
            statusDot.classList.add('active');
        } else {
            statusDot.classList.remove('active');
        }
    }

    if (statusText) {
        statusText.textContent = isActive ? 'Caméra active' : 'Caméra arrêtée';
    }
}

function updateScanStats() {
    const successfulScans = document.getElementById('successful-scans');
    const scanAttempts = document.getElementById('scan-attempts');

    if (successfulScans) {
        const successCount = qrScanner.scanHistory.filter(scan => scan.success).length;
        successfulScans.textContent = successCount;
    }

    if (scanAttempts) {
        scanAttempts.textContent = qrScanner.scanHistory.length;
    }
}

function resetQRScanner() {
    console.log('Réinitialisation du scanner QR...');

    // Arrêter la caméra si active
    if (qrScanner.isActive) {
        stopCamera();
    }

    // Réinitialiser les données
    qrScanner.currentBLData = null;

    // Masquer les sections de résultats
    const qrRawData = document.getElementById('qr-raw-data');
    const qrParsedData = document.getElementById('qr-parsed-data');
    const demoQRSection = document.getElementById('demo-qr-section');

    if (qrRawData) qrRawData.style.display = 'none';
    if (qrParsedData) qrParsedData.style.display = 'none';
    if (demoQRSection) demoQRSection.style.display = 'none';

    // Effacer l'image uploadée
    clearUploadedImage();

    // Mettre à jour les statistiques
    updateScanStats();
}

// Fonction simplifiée pour démo (sans vraie lib QR)
function simulateQRScan(data) {
    console.log('Simulation de scan QR:', data);

    try {
        // Essayer de parser comme JSON
        const blData = JSON.parse(data);

        if (blData.numeroBL && blData.dateBL) {
            displayQRResults(data, blData);
            addToScanHistory(blData.numeroBL, true);
            return true;
        }
    } catch (error) {
        console.log('Données QR non-JSON, traitement comme texte');
    }

    // Traitement comme texte simple
    displayRawQRData(data);
    addToScanHistory('Données texte', false);
    return false;
}

function displayRawQRData(data) {
    const qrRawData = document.getElementById('qr-raw-data');
    const qrRawContent = document.getElementById('qr-raw-content');

    if (qrRawData && qrRawContent) {
        qrRawContent.textContent = data;
        qrRawData.style.display = 'block';
    }
}

function displayQRResults(rawData, blData) {
    // Afficher les données brutes
    displayRawQRData(rawData);

    // Afficher les données interprétées
    const qrParsedData = document.getElementById('qr-parsed-data');
    if (qrParsedData) {
        qrParsedData.style.display = 'block';
    }

    // Remplir les informations du BL
    const parsedBLNumber = document.getElementById('parsed-bl-number');
    const parsedBLDate = document.getElementById('parsed-bl-date');
    const parsedBLSupplier = document.getElementById('parsed-bl-supplier');
    const parsedBLTotal = document.getElementById('parsed-bl-total');

    if (parsedBLNumber) parsedBLNumber.textContent = blData.numeroBL || '-';
    if (parsedBLDate) parsedBLDate.textContent = formatDate(blData.dateBL) || '-';
    if (parsedBLSupplier) parsedBLSupplier.textContent = blData.fournisseur || '-';
    if (parsedBLTotal) parsedBLTotal.textContent = (blData.montantBL || 0).toFixed(3) + ' DT';

    // Remplir le formulaire de correction
    const correctedBLNumber = document.getElementById('corrected-bl-number');
    const correctedBLDate = document.getElementById('corrected-bl-date');
    const correctedBLSupplier = document.getElementById('corrected-bl-supplier');

    if (correctedBLNumber) correctedBLNumber.value = blData.numeroBL || '';
    if (correctedBLDate) correctedBLDate.value = blData.dateBL || '';
    if (correctedBLSupplier) correctedBLSupplier.value = blData.fournisseur || '';

    // Afficher les articles
    displayParsedArticles(blData.medicaments || []);

    // Sauvegarder les données actuelles
    qrScanner.currentBLData = blData;

    // Afficher le bouton de sauvegarde
    const btnSaveScannedBL = document.getElementById('btn-save-scanned-bl');
    if (btnSaveScannedBL) {
        btnSaveScannedBL.style.display = 'inline-block';
    }
}

function displayParsedArticles(medicaments) {
    const tableBody = document.getElementById('parsed-articles-table-body');
    if (!tableBody) return;

    tableBody.innerHTML = '';

    for (let i = 0; i < medicaments.length; i++) {
        const med = medicaments[i];
        if (!med) continue;

        const row = document.createElement('tr');
        row.innerHTML = `
            <td><strong>${med.article || 'Article inconnu'}</strong></td>
            <td>${med.quantite || 0}</td>
            <td>${(med.prixUnitaire || 0).toFixed(3)} DT</td>
            <td>${(med.total || 0).toFixed(3)} DT</td>
            <td><span class="status-badge valid">✓ Valide</span></td>
        `;

        tableBody.appendChild(row);
    }
}

function addToScanHistory(blNumber, success) {
    const timestamp = new Date();
    const historyItem = {
        blNumber,
        success,
        timestamp
    };

    qrScanner.scanHistory.push(historyItem);
    updateScanHistoryDisplay();
    updateScanStats();
}

function updateScanHistoryDisplay() {
    const historyList = document.getElementById('scan-history-list');
    if (!historyList) return;

    historyList.innerHTML = '';

    // Afficher les 5 derniers scans
    const recentScans = qrScanner.scanHistory.slice(-5).reverse();

    for (let i = 0; i < recentScans.length; i++) {
        const scan = recentScans[i];
        if (!scan) continue;

        const div = document.createElement('div');
        div.className = 'history-item';

        div.innerHTML = `
            <div class="history-info">
                <div class="history-bl-number">${scan.blNumber}</div>
                <div class="history-timestamp">${formatDateTime(scan.timestamp)}</div>
            </div>
            <div class="history-actions">
                <span class="status-badge ${scan.success ? 'success' : 'error'}">
                    ${scan.success ? '✓ Succès' : '✗ Échec'}
                </span>
            </div>
        `;

        historyList.appendChild(div);
    }
}

function startScanning() {
    if (!qrScanner.video || !qrScanner.canvas || !qrScanner.context) {
        console.error('Elements vidéo non initialisés');
        return;
    }

    // Simulation de scan pour démo (sans vraie lib QR)
    qrScanner.scanInterval = setInterval(() => {
        if (!qrScanner.isActive) return;

        // Mettre à jour le statut
        const scanStatus = document.getElementById('scan-status');
        if (scanStatus) {
            scanStatus.textContent = 'Recherche de QR code...';
        }

        // Simulation: détecter un QR après 10 secondes (pour démo)
        // Dans une vraie implémentation, ici on analyserait les frames vidéo

    }, 1000);

    console.log('Scan démarré');
}

function switchCamera() {
    const cameraSelect = document.getElementById('camera-select');
    if (!cameraSelect || cameraSelect.options.length <= 1) {
        alert('Aucune autre caméra disponible');
        return;
    }

    const currentIndex = cameraSelect.selectedIndex;
    const nextIndex = (currentIndex + 1) % cameraSelect.options.length;

    if (nextIndex === 0) {
        // Passer au premier élément non-vide
        cameraSelect.selectedIndex = 1;
    } else {
        cameraSelect.selectedIndex = nextIndex;
    }

    switchToSelectedCamera();
}

async function switchToSelectedCamera() {
    if (qrScanner.isActive) {
        stopCamera();
        await new Promise(resolve => setTimeout(resolve, 500)); // Attendre un peu
        await startCamera();
    }
}

function handleFileUpload(event) {
    const file = event.target.files[0];
    if (file) {
        processUploadedFile(file);
    }
}

function handleDragOver(event) {
    event.preventDefault();
    event.currentTarget.classList.add('dragover');
}

function handleDragLeave(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('dragover');
}

function handleDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('dragover');

    const files = event.dataTransfer.files;
    if (files.length > 0) {
        processUploadedFile(files[0]);
    }
}

function processUploadedFile(file) {
    if (!file.type.startsWith('image/')) {
        alert('Veuillez sélectionner un fichier image.');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        displayUploadedImage(e.target.result);
    };
    reader.readAsDataURL(file);
}

function displayUploadedImage(imageSrc) {
    const uploadedImagePreview = document.getElementById('uploaded-image-preview');
    const uploadedQRImage = document.getElementById('uploaded-qr-image');

    if (uploadedQRImage) {
        uploadedQRImage.src = imageSrc;
    }

    if (uploadedImagePreview) {
        uploadedImagePreview.style.display = 'block';
    }

    // Ajouter les écouteurs pour les boutons d'action
    const btnScanUploadedImage = document.getElementById('btn-scan-uploaded-image');
    const btnClearUploadedImage = document.getElementById('btn-clear-uploaded-image');

    if (btnScanUploadedImage) {
        btnScanUploadedImage.onclick = () => scanUploadedImage(imageSrc);
    }

    if (btnClearUploadedImage) {
        btnClearUploadedImage.onclick = clearUploadedImage;
    }
}

function scanUploadedImage(imageSrc) {
    console.log('Scan de l\'image uploadée...');

    // Simulation de scan d'image (sans vraie lib QR)
    // Générer des données de démo
    const demoQRData = generateDemoQRData();
    const jsonData = JSON.stringify(demoQRData);

    simulateQRScan(jsonData);

    alert('Image scannée avec succès! (Données de démonstration)');
}

function clearUploadedImage() {
    const uploadedImagePreview = document.getElementById('uploaded-image-preview');
    const qrFileInput = document.getElementById('qr-file-input');

    if (uploadedImagePreview) {
        uploadedImagePreview.style.display = 'none';
    }

    if (qrFileInput) {
        qrFileInput.value = '';
    }
}

function toggleDemoQRSection() {
    const demoQRSection = document.getElementById('demo-qr-section');
    if (!demoQRSection) return;

    if (demoQRSection.style.display === 'none' || !demoQRSection.style.display) {
        demoQRSection.style.display = 'block';

        // Initialiser les écouteurs pour la section démo
        const btnGenerateQR = document.getElementById('btn-generate-qr');
        const btnDownloadQR = document.getElementById('btn-download-qr');

        if (btnGenerateQR) {
            btnGenerateQR.onclick = generateDemoQR;
        }

        if (btnDownloadQR) {
            btnDownloadQR.onclick = downloadGeneratedQR;
        }
    } else {
        demoQRSection.style.display = 'none';
    }
}

function generateDemoQR() {
    const demoBLNumber = document.getElementById('demo-bl-number')?.value || 'BL-DEMO-001';
    const demoBLDate = document.getElementById('demo-bl-date')?.value || new Date().toISOString().split('T')[0];
    const demoBLSupplier = document.getElementById('demo-bl-supplier')?.value || 'Pharmacie Démo';
    const demoArticlesCount = parseInt(document.getElementById('demo-articles-count')?.value || '2');

    const demoData = {
        numeroBL: demoBLNumber,
        dateBL: demoBLDate,
        fournisseur: demoBLSupplier,
        montantBL: 0,
        medicaments: []
    };

    // Générer des articles de démo
    const demoArticles = [
        { article: 'Paracétamol 500mg', quantite: 25, prixUnitaire: 12.500 },
        { article: 'Aspirine 100mg', quantite: 15, prixUnitaire: 8.750 },
        { article: 'Ibuprofène 400mg', quantite: 20, prixUnitaire: 15.250 },
        { article: 'Amoxicilline 500mg', quantite: 10, prixUnitaire: 22.500 },
        { article: 'Doliprane 1000mg', quantite: 30, prixUnitaire: 18.750 }
    ];

    for (let i = 0; i < demoArticlesCount && i < demoArticles.length; i++) {
        const article = demoArticles[i];
        article.total = article.quantite * article.prixUnitaire;
        demoData.medicaments.push(article);
        demoData.montantBL += article.total;
    }

    const qrData = JSON.stringify(demoData);

    // Afficher le QR généré (simulation)
    const generatedQRDisplay = document.getElementById('generated-qr-display');
    const generatedQRCode = document.getElementById('generated-qr-code');
    const btnDownloadQR = document.getElementById('btn-download-qr');

    if (generatedQRCode) {
        generatedQRCode.innerHTML = `
            <div style="
                width: 200px;
                height: 200px;
                background: #000;
                color: #fff;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                text-align: center;
                border-radius: 8px;
                padding: 1rem;
                word-break: break-all;
                line-height: 1.2;
            ">
                QR CODE<br>
                ${demoBLNumber}<br>
                <small>${demoBLDate}</small><br>
                <small>${demoArticlesCount} articles</small>
            </div>
        `;
    }

    if (generatedQRDisplay) {
        generatedQRDisplay.style.display = 'block';
    }

    if (btnDownloadQR) {
        btnDownloadQR.style.display = 'inline-block';
        btnDownloadQR.onclick = () => downloadQRData(qrData, demoBLNumber);
    }

    // Permettre de scanner ce QR généré
    generatedQRCode.onclick = () => {
        simulateQRScan(qrData);
        alert('QR code démo scanné avec succès!');
    };

    console.log('QR démo généré:', qrData);
}

function downloadQRData(qrData, filename) {
    const blob = new Blob([qrData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `qr-data-${filename}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);

    URL.revokeObjectURL(url);
}

function generateDemoQRData() {
    return {
        numeroBL: 'BL-SCAN-' + Date.now(),
        dateBL: new Date().toISOString().split('T')[0],
        fournisseur: 'Pharmacie Scannée',
        montantBL: 187.500,
        medicaments: [
            {
                article: 'Paracétamol 500mg',
                quantite: 10,
                prixUnitaire: 12.500,
                total: 125.000
            },
            {
                article: 'Aspirine 100mg',
                quantite: 5,
                prixUnitaire: 12.500,
                total: 62.500
            }
        ]
    };
}

function formatDateTime(date) {
    return new Date(date).toLocaleString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function testQRScan() {
    console.log('Test de scan QR démo...');

    // Générer des données de test
    const testQRData = {
        numeroBL: 'BL-TEST-' + Date.now(),
        dateBL: new Date().toISOString().split('T')[0],
        fournisseur: 'Pharmacie Test Scanner',
        montantBL: 275.750,
        medicaments: [
            {
                article: 'Paracétamol 500mg',
                quantite: 15,
                prixUnitaire: 12.500,
                total: 187.500
            },
            {
                article: 'Aspirine 100mg',
                quantite: 10,
                prixUnitaire: 8.825,
                total: 88.250
            }
        ]
    };

    const jsonData = JSON.stringify(testQRData);

    // Simuler la détection
    const scanStatus = document.getElementById('scan-status');
    if (scanStatus) {
        scanStatus.textContent = 'QR Code détecté! Traitement...';
        scanStatus.style.color = '#10b981';
    }

    // Traiter les données après un court délai
    setTimeout(() => {
        simulateQRScan(jsonData);

        if (scanStatus) {
            scanStatus.textContent = 'Scan terminé avec succès!';
        }

        alert('🎉 QR Code test scanné avec succès!\n\nBL détecté: ' + testQRData.numeroBL);
    }, 1500);
}

// Mettre à jour la fonction d'initialisation principale existante
// (Remplacer l'ancienne initialisation par celle-ci)
function initializeCompleteApp() {
    console.log('Initialisation complète de l\'application...');

    // Nouvelle initialisation des onglets
    initializeTabs();

    console.log('Initialisation complète terminée');
}

// Ajouter l'initialisation des onglets à l'initialisation existante
if (typeof window !== 'undefined') {
    const originalInit = window.addEventListener || function() {};

    // Attendre que le DOM soit prêt et que l'initialisation existante soit terminée
    setTimeout(() => {
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            initializeCompleteApp();
        } else {
            document.addEventListener('DOMContentLoaded', initializeCompleteApp);
        }
    }, 100);
}
