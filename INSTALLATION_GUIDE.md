# 🚀 GUIDE D'INSTALLATION - GESTION MÉDICAMENTS

## 📋 CONTENU DU PACKAGE

Ce package contient l'application "Gestion Médicaments" prête à utiliser sur Windows.

### 📁 Fichiers Inclus :
- **gestion-medicaments.exe** : Application principale
- **resources/** : Ressources de l'application
- **database.db** : Base de données (créée automatiquement si absente)
- **README.md** : Documentation
- **GUIDE_UTILISATION.md** : Guide d'utilisation

## 🔧 INSTALLATION

### Option 1 : Version Portable (Recommandée)
1. **Extraire** le dossier complet sur votre ordinateur
2. **Double-cliquer** sur `gestion-medicaments.exe`
3. **L'application se lance** immédiatement

### Option 2 : Installateur Windows
1. **Exécuter** le fichier `.exe` d'installation
2. **Suivre** les instructions d'installation
3. **Lancer** depuis le menu Démarrer ou le raccourci bureau

## ⚙️ CONFIGURATION REQUISE

### Système d'Exploitation :
- **Windows 10** ou plus récent (64-bit)
- **Windows 8.1** (64-bit) - compatible
- **Windows 7** (64-bit) - non testé

### Ressources :
- **RAM** : 4 GB minimum, 8 GB recommandé
- **Espace disque** : 500 MB libres
- **Processeur** : Intel/AMD 64-bit

## 🚀 PREMIER LANCEMENT

### 1. Vérification de l'Installation
- L'application crée automatiquement la base de données
- Les fichiers de configuration sont générés au premier lancement
- Aucune configuration manuelle requise

### 2. Import des Données (Optionnel)
- Utilisez l'onglet "Articles" pour importer vos articles depuis Excel
- Format supporté : .xlsx avec colonnes "Designation" et "Prix"

### 3. Création du Premier BL
- Allez sur l'onglet "Bons de Livraison"
- Cliquez "Nouveau Bon de Livraison"
- Remplissez les informations et ajoutez des articles

## 🔧 DÉPANNAGE

### Problème : L'application ne se lance pas
**Solutions :**
1. Vérifiez que vous avez les droits administrateur
2. Désactivez temporairement l'antivirus
3. Exécutez en tant qu'administrateur (clic droit → "Exécuter en tant qu'administrateur")

### Problème : Base de données corrompue
**Solutions :**
1. Supprimez le fichier `database.db`
2. Relancez l'application (nouvelle base créée automatiquement)
3. Réimportez vos données si nécessaire

### Problème : Erreur de permissions
**Solutions :**
1. Placez l'application dans un dossier utilisateur (ex: Documents)
2. Évitez les dossiers système (Program Files, etc.)
3. Exécutez avec des droits administrateur

## 📞 SUPPORT

### Raccourcis de Diagnostic :
- **Ctrl + E** : Test de l'API Electron
- **Ctrl + D** : Debug des données BL
- **Ctrl + A** : Test des articles
- **Ctrl + S** : Test des statistiques
- **F12** : Console de développement (pour debugging)

### Logs de Debugging :
- Ouvrez la console (F12) pour voir les logs détaillés
- Les erreurs sont affichées avec des messages explicites
- Utilisez les raccourcis de test pour diagnostiquer les problèmes

## 📋 FONCTIONNALITÉS PRINCIPALES

### ✅ Gestion des Bons de Livraison :
- Création, modification, suppression de BL
- Affichage consolidé (une ligne par BL)
- Calcul automatique des totaux

### ✅ Gestion des Articles :
- Import Excel des articles
- Base de données intégrée
- Recherche et sélection facilitées

### ✅ Statistiques :
- Statistiques globales en temps réel
- Calculs basés sur les données réelles
- Affichage par onglets organisés

### ✅ Fonctionnalités Avancées :
- Groupage de BL
- Scanner QR (si caméra disponible)
- Export des données
- Interface responsive

## 🎯 VERSION

**Version :** 1.0.0
**Date de compilation :** 01/06/2025
**Plateforme :** Windows 64-bit

---

**🎉 L'application est prête à utiliser ! Bon travail avec la gestion de vos médicaments !**
