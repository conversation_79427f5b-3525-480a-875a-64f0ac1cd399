/* Reset et base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

.app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Navigation principale */
.main-nav {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

.nav-brand h1 {
    font-size: 1.8rem;
    font-weight: 600;
    margin: 0;
}

.nav-tabs {
    display: flex;
    gap: 0.5rem;
}

.nav-tab {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-tab:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.nav-tab.active {
    background: rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Sections des onglets */
.tab-section {
    display: none !important;
    flex: 1;
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

.tab-section.active {
    display: block !important;
}

.tab-section.hidden {
    display: none !important;
}

/* S'assurer que seule la section active est visible */
#bons-livraison-section:not(.active),
#articles-section:not(.active),
#statistiques-section:not(.active),
#groupage-section:not(.active),
#qr-scanner-section:not(.active) {
    display: none !important;
    visibility: hidden !important;
}

/* En-têtes de section */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e1e5e9;
}

.section-header h2 {
    color: #333;
    font-size: 1.8rem;
    margin: 0;
}

.section-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

/* Header */
.app-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.app-header h1 {
    font-size: 1.8rem;
    font-weight: 600;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background-color: #4CAF50;
    color: white;
}

.btn-primary:hover {
    background-color: #45a049;
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-edit {
    background-color: #ffc107;
    color: #212529;
}

.btn-edit:hover {
    background-color: #e0a800;
}

.btn-info {
    background-color: #17a2b8;
    color: white;
}

.btn-info:hover {
    background-color: #138496;
}

/* Suggestions dropdown */
.suggestions-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e1e5e9;
    border-top: none;
    border-radius: 0 0 6px 6px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.suggestion-item {
    padding: 0.75rem;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
}

.suggestion-item:hover {
    background-color: #f8f9fa;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-item .article-name {
    font-weight: 500;
    color: #333;
}

.suggestion-item .article-details {
    font-size: 0.9rem;
    color: #666;
    margin-top: 0.25rem;
}

.form-group {
    position: relative;
}

/* Main content */
.app-main {
    flex: 1;
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

/* Form section */
.form-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.form-section.hidden {
    display: none;
}

.form-section.form-visible {
    display: block;
    animation: slideInDown 0.3s ease-out;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Styles pour le formulaire de modification */
.form-section {
    transition: all 0.3s ease;
}

.form-section h2 {
    color: #333;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

.form-section h2:before {
    content: '✏️';
    margin-right: 0.5rem;
}

.form-container h2 {
    margin-bottom: 1.5rem;
    color: #333;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #555;
}

.form-group input,
.form-group select {
    padding: 0.75rem;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
}

.medicaments-section {
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    padding: 1.5rem;
    margin: 1rem 0;
    background-color: #f8f9fa;
}

.medicaments-list {
    margin-top: 1rem;
}

.medicament-item {
    background: white;
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.medicament-info {
    flex: 1;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e1e5e9;
}

/* Table section */
.table-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.table-header h2 {
    color: #333;
}

.search-container {
    display: flex;
    gap: 1rem;
}

.search-input {
    padding: 0.75rem;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    width: 300px;
    font-size: 1rem;
}

.search-input:focus {
    outline: none;
    border-color: #667eea;
}

.table-container {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e1e5e9;
}

.data-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #555;
    position: sticky;
    top: 0;
}

.data-table tr:hover {
    background-color: #f8f9fa;
}

.data-table .actions {
    display: flex;
    gap: 0.5rem;
}

.data-table .actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* Empty state */
.empty-state {
    text-align: center;
    padding: 3rem;
    color: #6c757d;
}

.empty-state.hidden {
    display: none;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.empty-state h3 {
    margin-bottom: 0.5rem;
}

/* Stats section */
.stats-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #333;
    padding: 1.5rem;
    border-radius: 12px;
    text-align: center;
    border: 2px solid #dee2e6;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
    border-color: #667eea;
}

.stat-value {
    font-size: 2.2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.stat-label {
    font-size: 1rem;
    color: #6c757d;
    font-weight: 500;
    opacity: 1;
}

/* Statistiques principales pour les onglets */
.main-stats {
    margin-bottom: 2rem;
}

.stats-grid.large {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

/* Styles spécifiques pour les cadres de l'onglet BL */
.stats-section .stat-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    color: #333;
    border: 2px solid #e3f2fd;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    position: relative;
    overflow: hidden;
}

.stats-section .stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #2196F3, #4CAF50, #FF9800);
}

/* Icônes pour les statistiques BL */
.stats-section .stat-card:nth-child(1)::after {
    content: '📋';
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.5rem;
    opacity: 0.3;
}

.stats-section .stat-card:nth-child(2)::after {
    content: '💰';
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.5rem;
    opacity: 0.3;
}

.stats-section .stat-card:nth-child(3)::after {
    content: '📦';
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.5rem;
    opacity: 0.3;
}

.stats-section .stat-card:nth-child(1) {
    border-left: 4px solid #2196F3;
}

.stats-section .stat-card:nth-child(1) .stat-value {
    color: #2196F3;
}

.stats-section .stat-card:nth-child(2) {
    border-left: 4px solid #4CAF50;
}

.stats-section .stat-card:nth-child(2) .stat-value {
    color: #4CAF50;
}

.stats-section .stat-card:nth-child(3) {
    border-left: 4px solid #FF9800;
}

.stats-section .stat-card:nth-child(3) .stat-value {
    color: #FF9800;
}

/* Styles pour les autres onglets (conservent les couleurs originales) */
.stat-card.primary {
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
    color: white;
    border: none;
}

.stat-card.primary .stat-value,
.stat-card.primary .stat-label {
    color: white;
}

.stat-card.success {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    border: none;
}

.stat-card.success .stat-value,
.stat-card.success .stat-label {
    color: white;
}

.stat-card.warning {
    background: linear-gradient(135deg, #FF9800 0%, #f57c00 100%);
    color: white;
    border: none;
}

.stat-card.warning .stat-value,
.stat-card.warning .stat-label {
    color: white;
}

.stat-card.info {
    background: linear-gradient(135deg, #00BCD4 0%, #0097A7 100%);
    color: white;
    border: none;
}

.stat-card.info .stat-value,
.stat-card.info .stat-label {
    color: white;
}

.stat-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.stat-content {
    text-align: center;
}

/* Statistiques détaillées */
.detailed-stats {
    margin-top: 2rem;
}

.stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.stats-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.stats-card h3 {
    margin-bottom: 1rem;
    color: #333;
    font-size: 1.2rem;
}

.monthly-stats {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.month-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 6px;
}

.month-label {
    font-weight: 500;
    color: #555;
}

.month-value {
    font-weight: bold;
    color: #333;
}

/* Placeholders pour fonctionnalités en développement */
.feature-placeholder {
    text-align: center;
    padding: 3rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    color: #6c757d;
}

.placeholder-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Styles pour le module Groupage BL */
.groupage-step {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    border-left: 4px solid #667eea;
}

.step-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f0f0f0;
}

.step-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.3rem;
}

.step-info {
    color: #666;
    font-style: italic;
}

.selection-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.25rem;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: bold;
    color: #333;
}

.filters-section {
    display: flex;
    gap: 2rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f0f4f8;
    border-radius: 8px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 500;
    color: #333;
}

.filter-select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.summary-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.summary-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.summary-label {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-bottom: 0.5rem;
}

.summary-value {
    font-size: 1.5rem;
    font-weight: bold;
}

.grouped-preview {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.grouped-preview h4 {
    margin-bottom: 1rem;
    color: #333;
}

.final-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Styles pour le module Scanner QR */
.camera-selection {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    border: 2px solid #e3f2fd;
}

.camera-selection .form-group {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.camera-selection label {
    font-weight: 600;
    color: #333;
    min-width: 200px;
}

.camera-selection select {
    flex: 1;
    padding: 0.5rem;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
}

.camera-selection select:focus {
    border-color: #2196F3;
    outline: none;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.btn-sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
}

.scan-status {
    padding: 0.75rem 1rem;
    background: #f0f4f8;
    border-radius: 6px;
    border-left: 4px solid #2196F3;
    font-weight: 500;
    color: #333;
}

.scan-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.scan-container {
    position: relative;
    max-width: 600px;
    margin: 0 auto;
}

.camera-container {
    position: relative;
    background: #000;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.qr-video {
    width: 100%;
    height: 400px;
    object-fit: cover;
    background: #1a1a1a;
    border-radius: 12px;
    display: block;
    border: 2px solid #333;
}

.qr-video:not([src]) {
    background: linear-gradient(45deg, #1a1a1a 25%, #2a2a2a 25%, #2a2a2a 50%, #1a1a1a 50%, #1a1a1a 75%, #2a2a2a 75%);
    background-size: 20px 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.qr-video:not([src])::before {
    content: '📹 Caméra en attente...';
    color: #fff;
    font-size: 1.2rem;
    text-align: center;
    background: rgba(0,0,0,0.8);
    padding: 1rem 2rem;
    border-radius: 8px;
    border: 2px dashed #666;
}

.scan-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: none;
}

.scan-frame {
    position: relative;
    width: 250px;
    height: 250px;
    border: 2px solid rgba(255, 255, 255, 0.5);
}

.corner {
    position: absolute;
    width: 30px;
    height: 30px;
    border: 3px solid #00ff00;
}

.corner.top-left {
    top: -3px;
    left: -3px;
    border-right: none;
    border-bottom: none;
}

.corner.top-right {
    top: -3px;
    right: -3px;
    border-left: none;
    border-bottom: none;
}

.corner.bottom-left {
    bottom: -3px;
    left: -3px;
    border-right: none;
    border-top: none;
}

.corner.bottom-right {
    bottom: -3px;
    right: -3px;
    border-left: none;
    border-top: none;
}

.scan-line {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #00ff00, transparent);
    animation: scanLine 2s linear infinite;
}

@keyframes scanLine {
    0% { transform: translateY(-125px); }
    100% { transform: translateY(125px); }
}

.scan-instructions {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
}

.upload-zone {
    border: 2px dashed #ddd;
    border-radius: 12px;
    padding: 3rem;
    text-align: center;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.upload-zone:hover {
    border-color: #667eea;
    background: #f0f4f8;
}

.upload-zone.dragover {
    border-color: #00ff00;
    background: #e8f5e8;
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.upload-icon {
    font-size: 3rem;
    opacity: 0.5;
}

.camera-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    margin-top: 1rem;
    border: 1px solid #dee2e6;
}

.camera-status.success {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.camera-status.error {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.camera-status.warning {
    background: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #dc3545;
}

.status-dot.online {
    background: #28a745;
    animation: pulse 2s infinite;
}

.status-dot.offline {
    background: #dc3545;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.scan-stats {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
}

.scan-results-section,
.scan-history-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.results-header,
.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f0f0f0;
}

.results-actions,
.history-filters {
    display: flex;
    gap: 1rem;
}

.demo-actions {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f0f4f8;
    border-radius: 8px;
}

.demo-actions h4 {
    margin-bottom: 1rem;
    color: #333;
}

.demo-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.scan-results {
    min-height: 200px;
}

.scan-result-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid #28a745;
}

.scan-result-item.error {
    border-left-color: #dc3545;
    background: #fff5f5;
}

.scan-result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.scan-result-time {
    font-size: 0.9rem;
    color: #666;
}

.scan-result-status {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
}

.scan-result-status.success {
    background: #d4edda;
    color: #155724;
}

.scan-result-status.error {
    background: #f8d7da;
    color: #721c24;
}

.scan-result-content {
    font-size: 0.9rem;
    color: #333;
}

/* Responsive */
@media (max-width: 768px) {
    .app-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .header-actions {
        width: 100%;
        justify-content: center;
    }

    .table-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .search-input {
        width: 100%;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .app-main {
        padding: 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }
}
