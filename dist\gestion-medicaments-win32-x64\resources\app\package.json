{"name": "gestion-medicaments-desktop", "version": "1.0.0", "description": "Application desktop pour la gestion des médicaments dans les bons de livraison", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder", "build-win": "electron-builder --win --dir", "pack": "electron-builder --dir", "dist-win": "electron-builder --win --x64", "dist": "npm run build-win", "package-win": "electron-packager . gestion-medicaments --platform=win32 --arch=x64 --out=dist --overwrite"}, "keywords": ["medicaments", "pharmacie", "gestion", "bons-liv<PERSON>son"], "author": "Votre Nom", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1", "electron-packager": "^17.1.2"}, "build": {"appId": "com.gestion.medicaments", "productName": "Gestion Médicaments", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules", "!dist"], "win": {"target": "portable"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}, "dependencies": {"better-sqlite3": "^11.10.0", "electron-store": "^10.0.1", "xlsx": "^0.18.5"}}