<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Médicaments - Bons de Livraison</title>
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval'; script-src 'self' 'unsafe-inline' 'unsafe-eval';">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="app">
        <header class="app-header">
            <h1>🏥 Gestion des Médicaments - Bons de Livraison</h1>
            <div class="header-actions">
                <button id="btn-import-articles" class="btn btn-info">📁 Importer Articles</button>
                <button id="btn-nouveau" class="btn btn-primary">+ Nouveau Bon</button>
                <button id="btn-export" class="btn btn-secondary">📊 Exporter</button>
            </div>
        </header>

        <!-- Navigation par onglets -->
        <nav class="tab-navigation">
            <div class="nav-tabs">
                <button class="nav-tab active" data-tab="bons-livraison">
                    📋 Bons de Livraison
                </button>
                <button class="nav-tab" data-tab="qr-scanner">
                    📱 Scanner QR
                </button>
                <button class="nav-tab" data-tab="groupage">
                    📦 Groupage BL
                </button>
                <button class="nav-tab" data-tab="articles">
                    💊 Articles
                </button>
                <button class="nav-tab" data-tab="statistiques">
                    📊 Statistiques
                </button>
            </div>
        </nav>

        <main class="app-main">
            <!-- Section Bons de Livraison -->
            <div id="bons-livraison-section" class="tab-section active">
                <!-- Formulaire d'ajout/modification -->
            <div id="form-section" class="form-section hidden">
                <div class="form-container">
                    <h2 id="form-title">Nouveau Bon de Livraison</h2>
                    <form id="bon-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="numeroBL">Numéro BL *</label>
                                <input type="text" id="numeroBL" required>
                            </div>
                            <div class="form-group">
                                <label for="dateBL">Date BL *</label>
                                <input type="date" id="dateBL" required>
                            </div>
                            <div class="form-group">
                                <label for="fournisseur">Fournisseur</label>
                                <input type="text" id="fournisseur" placeholder="Nom du fournisseur">
                            </div>
                        </div>

                        <h3>Médicaments</h3>
                        <div class="medicaments-section">
                            <div class="medicament-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="article">Article *</label>
                                        <select id="article-select" style="display: none;">
                                            <option value="">Sélectionnez un article...</option>
                                        </select>
                                        <input type="text" id="article" placeholder="Tapez pour rechercher un médicament...">
                                        <div id="article-suggestions" class="suggestions-dropdown"></div>
                                    </div>
                                    <div class="form-group">
                                        <label for="quantite">Quantité *</label>
                                        <input type="number" id="quantite" min="1" placeholder="Quantité">
                                    </div>
                                    <div class="form-group">
                                        <label for="prixUnitaire">Prix Unitaire</label>
                                        <input type="number" id="prixUnitaire" step="0.01" placeholder="0.00">
                                    </div>
                                    <div class="form-group">
                                        <button type="button" id="btn-add-medicament" class="btn btn-small">+ Ajouter</button>
                                    </div>
                                </div>
                            </div>

                            <div id="medicaments-list" class="medicaments-list">
                                <!-- Les médicaments ajoutés apparaîtront ici -->
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="button" id="btn-cancel" class="btn btn-secondary">Annuler</button>
                            <button type="submit" class="btn btn-primary">Enregistrer</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Tableau des bons de livraison -->
            <div class="table-section">
                <div class="table-header">
                    <h2>Liste des Bons de Livraison</h2>
                    <div class="search-container">
                        <input type="text" id="search-input" placeholder="Rechercher..." class="search-input">
                    </div>
                </div>

                <div class="table-container">
                    <table id="bons-table" class="data-table">
                        <thead>
                            <tr>
                                <th>Numéro BL</th>
                                <th>Date BL</th>
                                <th>Fournisseur</th>
                                <th>Montant BL</th>
                                <th>Nb Articles</th>
                                <th>Quantité Totale</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="table-body">
                            <!-- Les données seront ajoutées dynamiquement -->
                        </tbody>
                    </table>
                </div>

                <div id="empty-state" class="empty-state">
                    <div class="empty-icon">📋</div>
                    <h3>Aucun bon de livraison</h3>
                    <p>Cliquez sur "Nouveau Bon" pour commencer</p>
                </div>
            </div>

                <!-- Statistiques rapides pour la section BL -->
                <div class="stats-section">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value" id="total-bons">0</div>
                            <div class="stat-label">Total Bons</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="total-montant">0 DT</div>
                            <div class="stat-label">Montant Total</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="total-quantite">0</div>
                            <div class="stat-label">Quantité Totale</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section QR Scanner -->
            <div id="qr-scanner-section" class="tab-section hidden">
                <div class="section-header">
                    <h2>📱 Scanner QR Code - Insertion Automatique BL</h2>
                    <div class="section-actions">
                        <button id="btn-toggle-camera" class="btn btn-primary">📹 Démarrer Caméra</button>
                        <button id="btn-upload-qr-image" class="btn btn-secondary">🖼️ Importer Image QR</button>
                        <button id="btn-generate-demo-qr" class="btn btn-outline">🎯 Générer QR Démo</button>
                    </div>
                </div>

                <!-- Scanner en temps réel -->
                <div class="qr-scanner-container">
                    <!-- Zone de scan caméra -->
                    <div class="camera-scanner-section">
                        <div class="scanner-header">
                            <h3>📹 Scanner en Temps Réel</h3>
                            <div class="scanner-controls">
                                <select id="camera-select" class="camera-select">
                                    <option value="">Sélectionner une caméra...</option>
                                </select>
                                <button id="btn-switch-camera" class="btn btn-small">🔄 Changer</button>
                            </div>
                        </div>

                        <div class="camera-container">
                            <video id="qr-video" class="qr-video" autoplay muted playsinline></video>
                            <canvas id="qr-canvas" class="qr-canvas" style="display: none;"></canvas>

                            <!-- Overlay de scan -->
                            <div class="scan-overlay">
                                <div class="scan-frame">
                                    <div class="scan-corners">
                                        <div class="corner top-left"></div>
                                        <div class="corner top-right"></div>
                                        <div class="corner bottom-left"></div>
                                        <div class="corner bottom-right"></div>
                                    </div>
                                    <div class="scan-line"></div>
                                </div>
                                <div class="scan-instructions">
                                    <p>🎯 Placez le QR code dans le cadre</p>
                                    <p class="scan-status" id="scan-status">En attente...</p>
                                </div>
                            </div>
                        </div>

                        <div class="camera-status">
                            <div class="status-indicator" id="camera-status">
                                <span class="status-dot"></span>
                                <span class="status-text">Caméra arrêtée</span>
                            </div>
                            <div class="scan-stats">
                                <span>Scans réussis: <strong id="successful-scans">0</strong></span>
                                <span>Tentatives: <strong id="scan-attempts">0</strong></span>
                            </div>
                        </div>

                        <!-- Bouton de test pour simuler un scan -->
                        <div class="test-scan-section" style="margin-top: 1rem; text-align: center;">
                            <button id="btn-test-scan" class="btn btn-outline">🎯 Tester Scan QR (Démo)</button>
                            <p style="font-size: 0.9rem; color: #64748b; margin-top: 0.5rem;">
                                Cliquez pour simuler la détection d'un QR code
                            </p>
                        </div>
                    </div>

                    <!-- Zone d'upload d'image -->
                    <div class="image-upload-section">
                        <div class="upload-header">
                            <h3>🖼️ Scanner depuis Image</h3>
                        </div>

                        <div class="upload-zone" id="qr-upload-zone">
                            <input type="file" id="qr-file-input" accept="image/*" style="display: none;">
                            <div class="upload-content">
                                <div class="upload-icon">🖼️</div>
                                <p>Glissez une image QR ici</p>
                                <p>ou <button type="button" class="upload-btn" onclick="document.getElementById('qr-file-input').click()">Parcourir</button></p>
                                <small>Formats supportés: JPG, PNG, GIF, WebP</small>
                            </div>
                        </div>

                        <div class="uploaded-image-preview" id="uploaded-image-preview" style="display: none;">
                            <img id="uploaded-qr-image" alt="QR Code uploadé">
                            <div class="image-actions">
                                <button id="btn-scan-uploaded-image" class="btn btn-primary">🔍 Scanner cette image</button>
                                <button id="btn-clear-uploaded-image" class="btn btn-secondary">🗑️ Effacer</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Résultats du scan -->
                <div class="scan-results-section">
                    <div class="results-header">
                        <h3>📊 Résultats du Scan</h3>
                        <div class="results-actions">
                            <button id="btn-clear-results" class="btn btn-secondary">🗑️ Effacer Résultats</button>
                            <button id="btn-save-scanned-bl" class="btn btn-success" style="display: none;">💾 Enregistrer BL</button>
                        </div>
                    </div>

                    <!-- Données brutes du QR -->
                    <div class="qr-raw-data" id="qr-raw-data" style="display: none;">
                        <h4>📜 Données Brutes QR</h4>
                        <div class="raw-data-container">
                            <pre id="qr-raw-content"></pre>
                            <button id="btn-copy-raw-data" class="btn btn-small">📋 Copier</button>
                        </div>
                    </div>

                    <!-- Données interprétées -->
                    <div class="qr-parsed-data" id="qr-parsed-data" style="display: none;">
                        <h4>📄 Bon de Livraison Détecté</h4>

                        <!-- Informations générales du BL -->
                        <div class="bl-info-card">
                            <div class="bl-header-info">
                                <div class="bl-number">
                                    <label>Numéro BL:</label>
                                    <span id="parsed-bl-number">-</span>
                                </div>
                                <div class="bl-date">
                                    <label>Date:</label>
                                    <span id="parsed-bl-date">-</span>
                                </div>
                                <div class="bl-supplier">
                                    <label>Fournisseur:</label>
                                    <span id="parsed-bl-supplier">-</span>
                                </div>
                                <div class="bl-total">
                                    <label>Montant Total:</label>
                                    <span id="parsed-bl-total">-</span>
                                </div>
                            </div>
                        </div>

                        <!-- Articles du BL -->
                        <div class="bl-articles-section">
                            <h5>💊 Articles Détectés</h5>
                            <div class="table-container">
                                <table id="parsed-articles-table" class="data-table">
                                    <thead>
                                        <tr>
                                            <th>Article</th>
                                            <th>Quantité</th>
                                            <th>Prix Unitaire</th>
                                            <th>Total</th>
                                            <th>Statut</th>
                                        </tr>
                                    </thead>
                                    <tbody id="parsed-articles-table-body">
                                        <!-- Articles seront ajoutés dynamiquement -->
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Validation et corrections -->
                        <div class="bl-validation-section">
                            <h5>⚙️ Validation et Corrections</h5>
                            <div class="validation-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="corrected-bl-number">Numéro BL:</label>
                                        <input type="text" id="corrected-bl-number" class="form-input" placeholder="BL-001">
                                    </div>
                                    <div class="form-group">
                                        <label for="corrected-bl-date">Date:</label>
                                        <input type="date" id="corrected-bl-date" class="form-input">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="corrected-bl-supplier">Fournisseur:</label>
                                        <input type="text" id="corrected-bl-supplier" class="form-input" placeholder="Nom du fournisseur">
                                    </div>
                                    <div class="form-group">
                                        <label for="corrected-bl-notes">Notes:</label>
                                        <input type="text" id="corrected-bl-notes" class="form-input" placeholder="Notes additionnelles">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Historique des scans -->
                    <div class="scan-history" id="scan-history">
                        <h4>📅 Historique des Scans</h4>
                        <div class="history-list" id="scan-history-list">
                            <!-- Historique sera ajouté dynamiquement -->
                        </div>
                    </div>
                </div>

                <!-- Générateur de QR démo -->
                <div class="demo-qr-section" id="demo-qr-section" style="display: none;">
                    <div class="demo-header">
                        <h3>🎯 Générateur QR Démo</h3>
                        <p>Générez des QR codes de test pour tester le scanner</p>
                    </div>

                    <div class="demo-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="demo-bl-number">Numéro BL:</label>
                                <input type="text" id="demo-bl-number" class="form-input" value="BL-DEMO-001">
                            </div>
                            <div class="form-group">
                                <label for="demo-bl-date">Date:</label>
                                <input type="date" id="demo-bl-date" class="form-input">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="demo-bl-supplier">Fournisseur:</label>
                                <input type="text" id="demo-bl-supplier" class="form-input" value="Pharmacie Démo">
                            </div>
                            <div class="form-group">
                                <label for="demo-articles-count">Nombre d'articles:</label>
                                <select id="demo-articles-count" class="form-input">
                                    <option value="1">1 article</option>
                                    <option value="2" selected>2 articles</option>
                                    <option value="3">3 articles</option>
                                    <option value="5">5 articles</option>
                                </select>
                            </div>
                        </div>
                        <div class="demo-actions">
                            <button id="btn-generate-qr" class="btn btn-primary">📱 Générer QR Code</button>
                            <button id="btn-download-qr" class="btn btn-secondary" style="display: none;">💾 Télécharger QR</button>
                        </div>
                    </div>

                    <div class="generated-qr-display" id="generated-qr-display" style="display: none;">
                        <h4>📱 QR Code Généré</h4>
                        <div class="qr-display-container">
                            <div id="generated-qr-code"></div>
                            <div class="qr-info">
                                <p><strong>Scannez ce QR code</strong> avec le scanner ci-dessus pour tester</p>
                                <p>Ou sauvegardez-le et importez-le comme image</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- État vide -->
                <div id="qr-scanner-empty-state" class="empty-state" style="display: none;">
                    <div class="empty-icon">📱</div>
                    <h3>Scanner QR Code Prêt</h3>
                    <p>Démarrez la caméra ou importez une image pour scanner un QR code</p>
                </div>
            </div>

            <!-- Section Groupage BL -->
            <div id="groupage-section" class="tab-section hidden">
                <div class="section-header">
                    <h2>📦 Groupage de Bons de Livraison</h2>
                    <div class="section-actions">
                        <button id="btn-reset-groupage" class="btn btn-secondary">🔄 Réinitialiser</button>
                        <button id="btn-create-grouped-bl" class="btn btn-success">📦 Créer BL Groupé</button>
                    </div>
                </div>

                <!-- Étape 1: Sélection des BL -->
                <div class="groupage-step">
                    <div class="step-header">
                        <h3>📝 Étape 1: Sélection des Bons de Livraison</h3>
                        <div class="step-actions">
                            <button id="btn-select-all-bl" class="btn btn-outline">✓ Tout sélectionner</button>
                            <button id="btn-deselect-all-bl" class="btn btn-outline">✗ Tout désélectionner</button>
                        </div>
                    </div>

                    <!-- Filtres pour les BL -->
                    <div class="groupage-filters">
                        <div class="filter-row">
                            <div class="filter-group">
                                <label>Fournisseur :</label>
                                <select id="groupage-filter-supplier" class="filter-select">
                                    <option value="all">Tous les fournisseurs</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label>Période :</label>
                                <select id="groupage-filter-period" class="filter-select">
                                    <option value="all">Toutes les périodes</option>
                                    <option value="today">Aujourd'hui</option>
                                    <option value="week">Cette semaine</option>
                                    <option value="month">Ce mois</option>
                                    <option value="custom">Période personnalisée</option>
                                </select>
                            </div>
                            <div class="filter-group" id="custom-date-range" style="display: none;">
                                <label>Du :</label>
                                <input type="date" id="groupage-date-from" class="filter-input">
                                <label>Au :</label>
                                <input type="date" id="groupage-date-to" class="filter-input">
                            </div>
                        </div>
                    </div>

                    <!-- Liste des BL disponibles -->
                    <div class="bl-selection-container">
                        <div class="selection-stats">
                            <span id="selected-bl-count">0</span> BL sélectionné(s) sur <span id="total-bl-count">0</span>
                            | Montant total: <span id="selected-total-amount">0.000 DT</span>
                        </div>

                        <div class="table-container">
                            <table id="bl-selection-table" class="data-table">
                                <thead>
                                    <tr>
                                        <th width="50px">
                                            <input type="checkbox" id="select-all-checkbox">
                                        </th>
                                        <th>Numéro BL</th>
                                        <th>Date</th>
                                        <th>Fournisseur</th>
                                        <th>Montant</th>
                                        <th>Articles</th>
                                        <th>Quantité Totale</th>
                                    </tr>
                                </thead>
                                <tbody id="bl-selection-table-body">
                                    <!-- Les BL seront ajoutés dynamiquement -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Étape 2: Configuration du BL groupé -->
                <div class="groupage-step">
                    <div class="step-header">
                        <h3>⚙️ Étape 2: Configuration du BL Groupé</h3>
                    </div>

                    <div class="grouped-bl-config">
                        <div class="config-row">
                            <div class="config-group">
                                <label for="grouped-bl-number">Numéro du BL Groupé :</label>
                                <input type="text" id="grouped-bl-number" class="form-input" placeholder="BL-GROUP-001" required>
                            </div>
                            <div class="config-group">
                                <label for="grouped-bl-date">Date du BL Groupé :</label>
                                <input type="date" id="grouped-bl-date" class="form-input" required>
                            </div>
                        </div>
                        <div class="config-row">
                            <div class="config-group full-width">
                                <label for="grouped-bl-supplier">Fournisseur Principal :</label>
                                <select id="grouped-bl-supplier" class="form-input">
                                    <option value="">Sélectionner un fournisseur</option>
                                    <option value="Groupage Multiple">Groupage Multiple</option>
                                </select>
                            </div>
                        </div>
                        <div class="config-row">
                            <div class="config-group full-width">
                                <label for="grouped-bl-notes">Notes / Commentaires :</label>
                                <textarea id="grouped-bl-notes" class="form-input" rows="3" placeholder="Notes sur le groupage des BL..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Étape 3: Aperçu du groupage -->
                <div class="groupage-step">
                    <div class="step-header">
                        <h3>🔍 Étape 3: Aperçu du Groupage</h3>
                    </div>

                    <!-- Résumé du groupage -->
                    <div class="groupage-summary">
                        <div class="summary-cards">
                            <div class="summary-card">
                                <div class="summary-icon">📋</div>
                                <div class="summary-content">
                                    <div class="summary-value" id="summary-bl-count">0</div>
                                    <div class="summary-label">BL Groupés</div>
                                </div>
                            </div>
                            <div class="summary-card">
                                <div class="summary-icon">💊</div>
                                <div class="summary-content">
                                    <div class="summary-value" id="summary-articles-count">0</div>
                                    <div class="summary-label">Articles Uniques</div>
                                </div>
                            </div>
                            <div class="summary-card">
                                <div class="summary-icon">📦</div>
                                <div class="summary-content">
                                    <div class="summary-value" id="summary-total-quantity">0</div>
                                    <div class="summary-label">Quantité Totale</div>
                                </div>
                            </div>
                            <div class="summary-card">
                                <div class="summary-icon">💰</div>
                                <div class="summary-content">
                                    <div class="summary-value" id="summary-total-amount">0.000 DT</div>
                                    <div class="summary-label">Montant Total</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Détail des articles groupés -->
                    <div class="grouped-articles-preview">
                        <h4>📊 Détail des Articles Groupés</h4>
                        <div class="table-container">
                            <table id="grouped-articles-table" class="data-table">
                                <thead>
                                    <tr>
                                        <th>Article</th>
                                        <th>Quantité Totale</th>
                                        <th>Prix Unitaire Moyen</th>
                                        <th>Montant Total</th>
                                        <th>Présent dans</th>
                                    </tr>
                                </thead>
                                <tbody id="grouped-articles-table-body">
                                    <!-- Les articles groupés seront ajoutés dynamiquement -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Liste des BL sources -->
                    <div class="source-bl-list">
                        <h4>📋 BL Sources du Groupage</h4>
                        <div id="source-bl-details" class="source-bl-container">
                            <!-- Les détails des BL sources seront ajoutés dynamiquement -->
                        </div>
                    </div>
                </div>

                <!-- État vide -->
                <div id="groupage-empty-state" class="empty-state">
                    <div class="empty-icon">📦</div>
                    <h3>Aucun BL disponible pour le groupage</h3>
                    <p>Créez d'abord des bons de livraison pour pouvoir les grouper</p>
                </div>
            </div>

            <!-- Section Articles -->
            <div id="articles-section" class="tab-section hidden">
                <div class="section-header">
                    <h2>💊 Gestion des Articles</h2>
                    <div class="section-actions">
                        <button id="btn-import-articles-tab" class="btn btn-primary">📁 Importer Articles</button>
                        <button id="btn-export-articles" class="btn btn-secondary">📊 Exporter Articles</button>
                        <button id="btn-add-article" class="btn btn-success">+ Nouvel Article</button>
                    </div>
                </div>

                <!-- Statistiques des articles -->
                <div class="articles-stats">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value" id="total-articles-count">0</div>
                            <div class="stat-label">Total Articles</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="articles-with-stock">0</div>
                            <div class="stat-label">Articles en Stock</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="average-price">0.000 DT</div>
                            <div class="stat-label">Prix Moyen</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="total-stock-value">0.000 DT</div>
                            <div class="stat-label">Valeur Stock Total</div>
                        </div>
                    </div>
                </div>

                <!-- Recherche et filtres -->
                <div class="articles-filters">
                    <div class="filter-row">
                        <div class="search-container">
                            <input type="text" id="articles-search" placeholder="Rechercher un article..." class="search-input">
                        </div>
                        <div class="filter-group">
                            <select id="articles-filter-stock" class="filter-select">
                                <option value="all">Tous les articles</option>
                                <option value="in-stock">En stock</option>
                                <option value="out-of-stock">Rupture de stock</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <select id="articles-sort" class="filter-select">
                                <option value="name-asc">Nom A-Z</option>
                                <option value="name-desc">Nom Z-A</option>
                                <option value="price-asc">Prix croissant</option>
                                <option value="price-desc">Prix décroissant</option>
                                <option value="stock-desc">Stock décroissant</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Tableau des articles -->
                <div class="table-container">
                    <table id="articles-table" class="data-table">
                        <thead>
                            <tr>
                                <th>Nom de l'Article</th>
                                <th>Prix Unitaire</th>
                                <th>Stock Actuel</th>
                                <th>Valeur Stock</th>
                                <th>Dernière Utilisation</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="articles-table-body">
                            <!-- Les articles seront ajoutés dynamiquement -->
                        </tbody>
                    </table>
                </div>

                <div id="articles-empty-state" class="empty-state">
                    <div class="empty-icon">💊</div>
                    <h3>Aucun article importé</h3>
                    <p>Cliquez sur "Importer Articles" pour commencer</p>
                </div>
            </div>

            <!-- Section Statistiques -->
            <div id="statistiques-section" class="tab-section hidden">
                <div class="section-header">
                    <h2>📊 Statistiques Générales</h2>
                    <div class="section-actions">
                        <button id="btn-refresh-stats" class="btn btn-primary">🔄 Actualiser</button>
                        <button id="btn-export-stats" class="btn btn-secondary">📊 Exporter Rapport</button>
                    </div>
                </div>

                <!-- Statistiques principales -->
                <div class="main-stats">
                    <div class="stats-grid large">
                        <div class="stat-card primary">
                            <div class="stat-icon">📋</div>
                            <div class="stat-content">
                                <div class="stat-value" id="stats-total-bons">0</div>
                                <div class="stat-label">Bons de Livraison Créés</div>
                            </div>
                        </div>
                        <div class="stat-card success">
                            <div class="stat-icon">💊</div>
                            <div class="stat-content">
                                <div class="stat-value" id="stats-total-articles">0</div>
                                <div class="stat-label">Articles Totaux</div>
                            </div>
                        </div>
                        <div class="stat-card warning">
                            <div class="stat-icon">💰</div>
                            <div class="stat-content">
                                <div class="stat-value" id="stats-montant-total">0.000 DT</div>
                                <div class="stat-label">Montant Total des BL</div>
                            </div>
                        </div>
                        <div class="stat-card info">
                            <div class="stat-icon">📦</div>
                            <div class="stat-content">
                                <div class="stat-value" id="stats-quantite-totale">0</div>
                                <div class="stat-label">Quantité Totale Livrée</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistiques détaillées -->
                <div class="detailed-stats">
                    <div class="stats-row">
                        <!-- Statistiques par mois -->
                        <div class="stats-card">
                            <h3>📅 Statistiques Mensuelles</h3>
                            <div class="monthly-stats">
                                <div class="month-stat">
                                    <span class="month-label">Ce mois</span>
                                    <span class="month-value" id="stats-this-month">0 BL</span>
                                </div>
                                <div class="month-stat">
                                    <span class="month-label">Mois dernier</span>
                                    <span class="month-value" id="stats-last-month">0 BL</span>
                                </div>
                                <div class="month-stat">
                                    <span class="month-label">Moyenne mensuelle</span>
                                    <span class="month-value" id="stats-avg-month">0 BL</span>
                                </div>
                            </div>
                        </div>

                        <!-- Top articles -->
                        <div class="stats-card">
                            <h3>🏆 Top Articles</h3>
                            <div id="top-articles-list" class="top-list">
                                <!-- Les top articles seront ajoutés dynamiquement -->
                            </div>
                        </div>
                    </div>

                    <div class="stats-row">
                        <!-- Statistiques par fournisseur -->
                        <div class="stats-card">
                            <h3>🏢 Fournisseurs</h3>
                            <div id="suppliers-stats" class="suppliers-list">
                                <!-- Les statistiques fournisseurs seront ajoutées dynamiquement -->
                            </div>
                        </div>

                        <!-- Tendances -->
                        <div class="stats-card">
                            <h3>📈 Tendances</h3>
                            <div class="trends-stats">
                                <div class="trend-item">
                                    <span class="trend-label">Croissance mensuelle</span>
                                    <span class="trend-value" id="growth-rate">+0%</span>
                                </div>
                                <div class="trend-item">
                                    <span class="trend-label">Article le plus commandé</span>
                                    <span class="trend-value" id="most-ordered">-</span>
                                </div>
                                <div class="trend-item">
                                    <span class="trend-label">Valeur moyenne par BL</span>
                                    <span class="trend-value" id="avg-bl-value">0.000 DT</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html>
