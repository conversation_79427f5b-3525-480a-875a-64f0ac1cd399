<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Médicaments - Bons de Livraison</title>
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval'; script-src 'self' 'unsafe-inline' 'unsafe-eval';">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="app">
        <!-- Navigation principale -->
        <nav class="main-nav">
            <div class="nav-container">
                <div class="nav-brand">
                    <h1>🏥 Gestion Médicaments</h1>
                </div>
                <div class="nav-tabs">
                    <button class="nav-tab active" data-tab="bons-livraison">
                        📋 Bons de Livraison
                    </button>
                    <button class="nav-tab" data-tab="articles">
                        💊 Articles
                    </button>
                    <button class="nav-tab" data-tab="statistiques">
                        📊 Statistiques
                    </button>
                    <button class="nav-tab" data-tab="groupage">
                        📦 Groupage BL
                    </button>
                    <button class="nav-tab" data-tab="qr-scanner">
                        📱 Scanner QR
                    </button>
                </div>
            </div>
        </nav>

        <!-- Section Bons de Livraison -->
        <main id="bons-livraison-section" class="tab-section active">
            <!-- Formulaire de création/modification -->
            <div id="form-section" class="form-section hidden">
                <div class="form-container">
                    <h2 id="form-title">Nouveau Bon de Livraison</h2>
                    <form id="bon-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="numeroBL">Numéro BL</label>
                                <input type="text" id="numeroBL" required>
                            </div>
                            <div class="form-group">
                                <label for="dateBL">Date BL</label>
                                <input type="date" id="dateBL" required>
                            </div>
                            <div class="form-group">
                                <label for="fournisseur">Fournisseur</label>
                                <input type="text" id="fournisseur" required>
                            </div>
                        </div>

                        <!-- Section médicaments -->
                        <div class="medicaments-section">
                            <h3>Ajouter un médicament</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="article">Article</label>
                                    <input type="text" id="article" placeholder="Nom du médicament">
                                    <div id="suggestions-dropdown" class="suggestions-dropdown"></div>
                                </div>
                                <div class="form-group">
                                    <label for="quantite">Quantité</label>
                                    <input type="number" id="quantite" min="1" step="1">
                                </div>
                                <div class="form-group">
                                    <label for="prixUnitaire">Prix unitaire (DT)</label>
                                    <input type="number" id="prixUnitaire" min="0" step="0.001">
                                </div>
                            </div>
                            <button type="button" id="btn-add-medicament" class="btn btn-secondary">+ Ajouter</button>

                            <!-- Liste des médicaments ajoutés -->
                            <div id="medicaments-list" class="medicaments-list"></div>
                        </div>

                        <div class="form-actions">
                            <button type="button" id="btn-cancel" class="btn btn-secondary">Annuler</button>
                            <button type="submit" class="btn btn-primary">Enregistrer</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Actions principales -->
            <div class="section-actions">
                <button id="btn-nouveau" class="btn btn-primary">+ Nouveau Bon de Livraison</button>
                <button id="btn-import-articles" class="btn btn-info">📁 Importer Articles</button>
                <button id="btn-export" class="btn btn-secondary">📊 Exporter</button>
            </div>

            <!-- Tableau des bons de livraison -->
            <div class="table-section">
                <div class="table-header">
                    <h2>Liste des Bons de Livraison</h2>
                    <div class="search-container">
                        <input type="text" id="search-input" placeholder="Rechercher..." class="search-input">
                    </div>
                </div>

                <div class="table-container">
                    <table id="bons-table" class="data-table">
                        <thead>
                            <tr>
                                <th>Numéro BL</th>
                                <th>Date BL</th>
                                <th>Montant BL</th>
                                <th>Article</th>
                                <th>Quantité</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="table-body">
                            <!-- Les données seront ajoutées dynamiquement -->
                        </tbody>
                    </table>
                </div>

                <div id="empty-state" class="empty-state">
                    <div class="empty-icon">📋</div>
                    <h3>Aucun bon de livraison</h3>
                    <p>Cliquez sur "Nouveau Bon de Livraison" pour commencer</p>
                </div>
            </div>

            <!-- Statistiques -->
            <div class="stats-section">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" id="total-bons">0</div>
                        <div class="stat-label">Total Bons</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="total-montant">0 DT</div>
                        <div class="stat-label">Montant Total</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="total-quantite">0</div>
                        <div class="stat-label">Quantité Totale</div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Section Articles -->
        <main id="articles-section" class="tab-section hidden" style="display: none;">
            <div class="section-header">
                <h2>💊 Gestion des Articles</h2>
                <div class="section-actions">
                    <button id="btn-import-articles-tab" class="btn btn-primary">📁 Importer Excel</button>
                    <button id="btn-export-articles" class="btn btn-secondary">📊 Exporter Articles</button>
                    <button id="btn-refresh-articles" class="btn btn-info">🔄 Actualiser</button>
                </div>
            </div>

            <!-- Statistiques des articles -->
            <div class="main-stats">
                <div class="stats-grid large">
                    <div class="stat-card primary">
                        <div class="stat-icon">💊</div>
                        <div class="stat-content">
                            <div class="stat-value" id="total-articles-count">0</div>
                            <div class="stat-label">Total Articles</div>
                        </div>
                    </div>
                    <div class="stat-card success">
                        <div class="stat-icon">✅</div>
                        <div class="stat-content">
                            <div class="stat-value" id="articles-with-stock">0</div>
                            <div class="stat-label">Articles en Stock</div>
                        </div>
                    </div>
                    <div class="stat-card warning">
                        <div class="stat-icon">💰</div>
                        <div class="stat-content">
                            <div class="stat-value" id="average-price">0.000 DT</div>
                            <div class="stat-label">Prix Moyen</div>
                        </div>
                    </div>
                    <div class="stat-card info">
                        <div class="stat-icon">📊</div>
                        <div class="stat-content">
                            <div class="stat-value" id="total-stock-value">0.000 DT</div>
                            <div class="stat-label">Valeur Stock Total</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tableau des articles -->
            <div class="table-section">
                <div class="table-header">
                    <h3>Liste des Articles</h3>
                    <div class="search-container">
                        <input type="text" id="articles-search" placeholder="Rechercher un article..." class="search-input">
                    </div>
                </div>

                <div class="table-container">
                    <table id="articles-table" class="data-table">
                        <thead>
                            <tr>
                                <th>Désignation</th>
                                <th>Prix Unitaire</th>
                                <th>Stock Actuel</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="articles-table-body">
                            <!-- Les données seront ajoutées dynamiquement -->
                        </tbody>
                    </table>
                </div>

                <div id="articles-empty-state" class="empty-state">
                    <div class="empty-icon">💊</div>
                    <h3>Aucun article</h3>
                    <p>Importez des articles depuis un fichier Excel pour commencer</p>
                </div>
            </div>
        </main>

        <!-- Section Statistiques -->
        <main id="statistiques-section" class="tab-section hidden" style="display: none;">
            <div class="section-header">
                <h2>📊 Statistiques Globales</h2>
                <div class="section-actions">
                    <button id="btn-refresh-stats" class="btn btn-primary">🔄 Actualiser</button>
                    <button id="btn-export-stats" class="btn btn-secondary">📊 Exporter</button>
                </div>
            </div>

            <!-- Statistiques principales -->
            <div class="main-stats">
                <div class="stats-grid large">
                    <div class="stat-card primary">
                        <div class="stat-icon">📋</div>
                        <div class="stat-content">
                            <div class="stat-value" id="stats-total-bons">0</div>
                            <div class="stat-label">Total Bons de Livraison</div>
                        </div>
                    </div>
                    <div class="stat-card success">
                        <div class="stat-icon">💊</div>
                        <div class="stat-content">
                            <div class="stat-value" id="stats-total-articles">0</div>
                            <div class="stat-label">Total Articles</div>
                        </div>
                    </div>
                    <div class="stat-card warning">
                        <div class="stat-icon">💰</div>
                        <div class="stat-content">
                            <div class="stat-value" id="stats-montant-total">0.000 DT</div>
                            <div class="stat-label">Montant Total</div>
                        </div>
                    </div>
                    <div class="stat-card info">
                        <div class="stat-icon">📦</div>
                        <div class="stat-content">
                            <div class="stat-value" id="stats-quantite-totale">0</div>
                            <div class="stat-label">Quantité Totale Livrée</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistiques détaillées -->
            <div class="detailed-stats">
                <div class="stats-row">
                    <!-- Statistiques par mois -->
                    <div class="stats-card">
                        <h3>📅 Statistiques Mensuelles</h3>
                        <div class="monthly-stats">
                            <div class="month-stat">
                                <span class="month-label">Ce mois</span>
                                <span class="month-value" id="stats-this-month">0 BL</span>
                            </div>
                            <div class="month-stat">
                                <span class="month-label">Mois dernier</span>
                                <span class="month-value" id="stats-last-month">0 BL</span>
                            </div>
                            <div class="month-stat">
                                <span class="month-label">Moyenne mensuelle</span>
                                <span class="month-value" id="stats-avg-month">0 BL</span>
                            </div>
                        </div>
                    </div>

                    <!-- Top articles -->
                    <div class="stats-card">
                        <h3>🏆 Top Articles</h3>
                        <div class="top-list" id="top-articles-list">
                            <!-- Généré dynamiquement -->
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Section Groupage BL -->
        <main id="groupage-section" class="tab-section hidden" style="display: none;">
            <div class="section-header">
                <h2>📦 Groupage de Bons de Livraison</h2>
                <div class="section-actions">
                    <button id="btn-reset-groupage" class="btn btn-secondary">🔄 Réinitialiser</button>
                    <button id="btn-create-grouped-bl" class="btn btn-primary" disabled>✅ Créer BL Groupé</button>
                </div>
            </div>

            <!-- Étape 1: Sélection des BL -->
            <div class="groupage-step">
                <div class="step-header">
                    <h3>🔴 Étape 1: Sélection des Bons de Livraison</h3>
                    <div class="step-info">
                        <span class="info-text">Sélectionnez les BL à grouper (minimum 2)</span>
                    </div>
                </div>

                <div class="selection-stats">
                    <div class="stat-item">
                        <span class="stat-label">BL sélectionnés:</span>
                        <span class="stat-value" id="selected-bl-count">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Montant total:</span>
                        <span class="stat-value" id="selected-total-amount">0.000 DT</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Articles différents:</span>
                        <span class="stat-value" id="selected-articles-count">0</span>
                    </div>
                </div>

                <div class="filters-section">
                    <div class="filter-group">
                        <label for="supplier-filter">Filtrer par fournisseur:</label>
                        <select id="supplier-filter" class="filter-select">
                            <option value="">Tous les fournisseurs</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="date-filter">Filtrer par période:</label>
                        <select id="date-filter" class="filter-select">
                            <option value="">Toutes les dates</option>
                            <option value="today">Aujourd'hui</option>
                            <option value="week">Cette semaine</option>
                            <option value="month">Ce mois</option>
                        </select>
                    </div>
                </div>

                <div class="table-container">
                    <table id="available-bls-table" class="data-table">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="select-all-bls">
                                    <label for="select-all-bls">Tout</label>
                                </th>
                                <th>Numéro BL</th>
                                <th>Date</th>
                                <th>Fournisseur</th>
                                <th>Montant</th>
                                <th>Articles</th>
                                <th>Quantité</th>
                            </tr>
                        </thead>
                        <tbody id="available-bls-body">
                            <!-- Généré dynamiquement -->
                        </tbody>
                    </table>
                </div>

                <div id="no-bls-message" class="empty-state" style="display: none;">
                    <div class="empty-icon">📦</div>
                    <h3>Aucun bon de livraison disponible</h3>
                    <p>Créez des bons de livraison pour pouvoir les grouper</p>
                </div>
            </div>

            <!-- Étape 2: Configuration du BL groupé -->
            <div class="groupage-step" id="step-2" style="display: none;">
                <div class="step-header">
                    <h3>🔵 Étape 2: Configuration du BL Groupé</h3>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="grouped-bl-number">Numéro du BL Groupé</label>
                        <input type="text" id="grouped-bl-number" readonly>
                    </div>
                    <div class="form-group">
                        <label for="grouped-bl-date">Date du BL Groupé</label>
                        <input type="date" id="grouped-bl-date">
                    </div>
                    <div class="form-group">
                        <label for="grouped-bl-supplier">Fournisseur Principal</label>
                        <input type="text" id="grouped-bl-supplier" readonly>
                    </div>
                </div>

                <div class="form-group">
                    <label for="grouped-bl-notes">Notes (optionnel)</label>
                    <textarea id="grouped-bl-notes" rows="3" placeholder="Notes sur ce groupage..."></textarea>
                </div>
            </div>

            <!-- Étape 3: Résumé et création -->
            <div class="groupage-step" id="step-3" style="display: none;">
                <div class="step-header">
                    <h3>🔶 Étape 3: Résumé et Création</h3>
                </div>

                <div class="summary-cards">
                    <div class="summary-card">
                        <div class="summary-icon">📋</div>
                        <div class="summary-label">BL à grouper</div>
                        <div class="summary-value" id="summary-bl-count">0</div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-icon">💊</div>
                        <div class="summary-label">Articles différents</div>
                        <div class="summary-value" id="summary-articles-count">0</div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-icon">📦</div>
                        <div class="summary-label">Quantité totale</div>
                        <div class="summary-value" id="summary-total-quantity">0</div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-icon">💰</div>
                        <div class="summary-label">Montant total</div>
                        <div class="summary-value" id="summary-total-amount">0.000 DT</div>
                    </div>
                </div>

                <div class="grouped-preview" id="grouped-preview">
                    <h4>🔍 Aperçu du BL Groupé</h4>
                    <div class="preview-content">
                        <!-- Généré dynamiquement -->
                    </div>
                </div>

                <div class="final-actions">
                    <button id="btn-back-to-selection" class="btn btn-secondary">⬅️ Retour à la sélection</button>
                    <button id="btn-confirm-groupage" class="btn btn-primary">✅ Confirmer et Créer</button>
                </div>
            </div>
        </main>

        <!-- Section Scanner QR -->
        <main id="qr-scanner-section" class="tab-section hidden" style="display: none;">
            <div class="section-header">
                <h2>📱 Scanner QR - Création Automatique de BL</h2>
                <div class="section-actions">
                    <button id="btn-toggle-camera" class="btn btn-primary">📷 Démarrer Caméra</button>
                    <button id="btn-upload-qr" class="btn btn-info">📁 Charger Image QR</button>
                </div>
            </div>

            <!-- Sélection de caméra -->
            <div class="camera-selection">
                <div class="form-group">
                    <label for="camera-select">📹 Sélectionner une caméra:</label>
                    <select id="camera-select" class="form-control">
                        <option value="">Chargement des caméras...</option>
                    </select>
                    <button id="btn-refresh-cameras" class="btn btn-secondary btn-sm">🔄 Actualiser</button>
                </div>
                <div id="scan-status" class="scan-status">
                    📹 Prêt à scanner
                </div>
            </div>

            <!-- Zone de scan -->
            <div class="scan-section">
                <div class="scan-container">
                    <div class="camera-container" id="camera-container" style="display: none;">
                        <video id="qr-video" class="qr-video" autoplay muted playsinline></video>
                        <div class="scan-overlay">
                            <div class="scan-frame">
                                <div class="corner top-left"></div>
                                <div class="corner top-right"></div>
                                <div class="corner bottom-left"></div>
                                <div class="corner bottom-right"></div>
                                <div class="scan-line"></div>
                            </div>
                            <div class="scan-instructions">
                                <p>🔍 Placez le QR code dans le cadre</p>
                            </div>
                        </div>
                    </div>

                    <div class="upload-zone" id="qr-upload-zone">
                        <div class="upload-content">
                            <div class="upload-icon">📷</div>
                            <h3>Scanner un QR Code</h3>
                            <p>Démarrez la caméra ou glissez une image ici</p>
                            <input type="file" id="qr-file-input" accept="image/*" hidden>
                            <button id="btn-select-file" class="btn btn-secondary">📁 Sélectionner une image</button>
                        </div>
                    </div>
                </div>

                <div class="camera-status">
                    <div class="status-indicator" id="camera-status">
                        <span class="status-dot offline"></span>
                        <span class="status-text">Caméra arrêtée</span>
                    </div>
                    <div class="scan-stats">
                        <span>QR scannés: <strong id="scanned-count">0</strong></span>
                        <span>BL créés: <strong id="created-bl-count">0</strong></span>
                        <span>Erreurs: <strong id="error-count">0</strong></span>
                    </div>
                </div>
            </div>

            <!-- Résultats du scan -->
            <div class="scan-results-section">
                <div class="results-header">
                    <h3>📋 Résultats du Scan</h3>
                    <div class="results-actions">
                        <button id="btn-clear-results" class="btn btn-secondary">🗑️ Effacer</button>
                        <button id="btn-export-results" class="btn btn-info">📊 Exporter</button>
                    </div>
                </div>

                <!-- Actions de test -->
                <div class="demo-actions">
                    <h4>🧪 Tests et Démonstration</h4>
                    <div class="demo-buttons">
                        <button id="btn-demo-qr-1" class="btn btn-info">🧪 Test QR #1 (Paracétamol)</button>
                        <button id="btn-demo-qr-2" class="btn btn-info">🧪 Test QR #2 (Aspirine)</button>
                        <button id="btn-demo-qr-3" class="btn btn-info">🧪 Test QR #3 (Multi-articles)</button>
                        <button id="btn-demo-qr-error" class="btn btn-warning">⚠️ Test QR Invalide</button>
                    </div>
                </div>

                <div id="scan-results" class="scan-results">
                    <!-- Résultats affichés ici -->
                </div>

                <div id="scan-empty-state" class="empty-state">
                    <div class="empty-icon">📱</div>
                    <h3>Aucun QR Code scanné</h3>
                    <p>Démarrez la caméra ou chargez une image pour commencer</p>
                </div>
            </div>

            <!-- Historique des scans -->
            <div class="scan-history-section">
                <div class="history-header">
                    <h3>📅 Historique des Scans</h3>
                    <div class="history-filters">
                        <select id="history-filter" class="filter-select">
                            <option value="all">Tous les scans</option>
                            <option value="success">Succès uniquement</option>
                            <option value="error">Erreurs uniquement</option>
                            <option value="today">Aujourd'hui</option>
                        </select>
                    </div>
                </div>

                <div class="table-container">
                    <table id="scan-history-table" class="data-table">
                        <thead>
                            <tr>
                                <th>Heure</th>
                                <th>Type</th>
                                <th>Contenu QR</th>
                                <th>Statut</th>
                                <th>BL Créé</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="scan-history-body">
                            <!-- Généré dynamiquement -->
                        </tbody>
                    </table>
                </div>

                <div id="history-empty-state" class="empty-state" style="display: none;">
                    <div class="empty-icon">📅</div>
                    <h3>Aucun historique</h3>
                    <p>L'historique des scans apparaîtra ici</p>
                </div>
            </div>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html>
