/* Reset et base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

.app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.app-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.app-header h1 {
    font-size: 1.8rem;
    font-weight: 600;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background-color: #4CAF50;
    color: white;
}

.btn-primary:hover {
    background-color: #45a049;
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-edit {
    background-color: #ffc107;
    color: #212529;
}

.btn-edit:hover {
    background-color: #e0a800;
}

.btn-info {
    background-color: #17a2b8;
    color: white;
}

.btn-info:hover {
    background-color: #138496;
}

/* Suggestions dropdown */
.suggestions-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e1e5e9;
    border-top: none;
    border-radius: 0 0 6px 6px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.suggestion-item {
    padding: 0.75rem;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
}

.suggestion-item:hover {
    background-color: #f8f9fa;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-item .article-name {
    font-weight: 500;
    color: #333;
}

.suggestion-item .article-details {
    font-size: 0.9rem;
    color: #666;
    margin-top: 0.25rem;
}

.form-group {
    position: relative;
}

/* Main content */
.app-main {
    flex: 1;
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

/* Form section */
.form-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.form-section.hidden {
    display: none;
}

.form-container h2 {
    margin-bottom: 1.5rem;
    color: #333;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #555;
}

.form-group input,
.form-group select {
    padding: 0.75rem;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
}

.medicaments-section {
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    padding: 1.5rem;
    margin: 1rem 0;
    background-color: #f8f9fa;
}

.medicaments-list {
    margin-top: 1rem;
}

.medicament-item {
    background: white;
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.medicament-info {
    flex: 1;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e1e5e9;
}

/* Table section */
.table-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.table-header h2 {
    color: #333;
}

.search-container {
    display: flex;
    gap: 1rem;
}

.search-input {
    padding: 0.75rem;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    width: 300px;
    font-size: 1rem;
}

.search-input:focus {
    outline: none;
    border-color: #667eea;
}

.table-container {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e1e5e9;
}

.data-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #555;
    position: sticky;
    top: 0;
}

.data-table tr:hover {
    background-color: #f8f9fa;
}

.data-table .actions {
    display: flex;
    gap: 0.5rem;
}

.data-table .actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* Empty state */
.empty-state {
    text-align: center;
    padding: 3rem;
    color: #6c757d;
}

.empty-state.hidden {
    display: none;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.empty-state h3 {
    margin-bottom: 0.5rem;
}

/* Stats section */
.stats-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Responsive */
@media (max-width: 768px) {
    .app-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .header-actions {
        width: 100%;
        justify-content: center;
    }

    .table-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .search-input {
        width: 100%;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .app-main {
        padding: 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }
}

/* ===== NAVIGATION PAR ONGLETS ===== */
.tab-navigation {
    background: white;
    border-bottom: 1px solid #e1e5e9;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.nav-tabs {
    display: flex;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.nav-tab {
    background: none;
    border: none;
    padding: 1rem 2rem;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    color: #64748b;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    position: relative;
}

.nav-tab:hover {
    color: #3b82f6;
    background-color: #f8fafc;
}

.nav-tab.active {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
    background-color: #f8fafc;
}

.nav-tab.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 3px;
    background: #3b82f6;
    border-radius: 2px 2px 0 0;
}

/* ===== SECTIONS D'ONGLETS ===== */
.tab-section {
    display: none !important;
    opacity: 0;
    visibility: hidden;
    position: relative;
    width: 100%;
    min-height: 500px;
    background: #f5f7fa;
    padding: 2rem 0;
    transition: opacity 0.2s ease-in-out;
}

.tab-section.active {
    display: block !important;
    opacity: 1;
    visibility: visible;
}

.tab-section.hidden {
    display: none !important;
    opacity: 0;
    visibility: hidden;
}

/* Isolation complète des sections */
.tab-section * {
    box-sizing: border-box;
}

/* Conteneur principal pour éviter les fuites */
.app-main {
    position: relative;
    overflow: hidden;
    background: #f5f7fa;
}

/* ===== ISOLATION DES SECTIONS ===== */
#bons-livraison-section {
    background: #f5f7fa;
    position: relative;
    z-index: 1;
}

#articles-section {
    background: #f5f7fa;
    position: relative;
    z-index: 2;
}

#statistiques-section {
    background: #f5f7fa;
    position: relative;
    z-index: 3;
}

#groupage-section {
    background: #f5f7fa;
    position: relative;
    z-index: 4;
}

#qr-scanner-section {
    background: #f5f7fa;
    position: relative;
    z-index: 5;
}

/* Empêcher les fuites de contenu */
.tab-section .table-container,
.tab-section .stats-section,
.tab-section .form-container {
    position: relative;
    z-index: 10;
    background: white;
    border-radius: 12px;
    margin: 1rem 2rem;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

/* ===== SECTION ARTICLES ===== */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 0 2rem;
    position: relative;
    z-index: 15;
}

.section-header h2 {
    font-size: 1.8rem;
    color: #1e293b;
    font-weight: 600;
}

.section-actions {
    display: flex;
    gap: 1rem;
}

.articles-stats {
    margin-bottom: 2rem;
    padding: 0 2rem;
}

.articles-filters {
    background: white;
    padding: 1.5rem 2rem;
    margin-bottom: 2rem;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.filter-row {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-select {
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    background: white;
    font-size: 0.9rem;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.filter-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* ===== SECTION STATISTIQUES ===== */
.main-stats {
    margin-bottom: 3rem;
    padding: 0 2rem;
}

.stats-grid.large {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.stat-card.primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
}

.stat-card.success {
    background: linear-gradient(135deg, #10b981 0%, #047857 100%);
    color: white;
}

.stat-card.warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
}

.stat-card.info {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: white;
}

.stat-card .stat-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.stat-card .stat-content {
    text-align: left;
}

.stat-card .stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-card .stat-label {
    font-size: 1rem;
    opacity: 0.9;
    font-weight: 500;
}

/* ===== STATISTIQUES DÉTAILLÉES ===== */
.detailed-stats {
    padding: 0 2rem;
}

.stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.stats-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    border: 1px solid #e5e7eb;
}

.stats-card h3 {
    font-size: 1.3rem;
    color: #1e293b;
    margin-bottom: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.monthly-stats {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.month-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 4px solid #3b82f6;
}

.month-label {
    font-weight: 500;
    color: #64748b;
}

.month-value {
    font-weight: 600;
    color: #1e293b;
    font-size: 1.1rem;
}

.top-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.top-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 4px solid #10b981;
}

.top-item-name {
    font-weight: 500;
    color: #1e293b;
}

.top-item-value {
    font-weight: 600;
    color: #10b981;
}

.suppliers-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.supplier-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 4px solid #8b5cf6;
}

.supplier-name {
    font-weight: 500;
    color: #1e293b;
}

.supplier-stats {
    text-align: right;
    font-size: 0.9rem;
    color: #64748b;
}

.trends-stats {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.trend-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 4px solid #f59e0b;
}

.trend-label {
    font-weight: 500;
    color: #64748b;
}

.trend-value {
    font-weight: 600;
    color: #1e293b;
    font-size: 1.1rem;
}

/* ===== RESPONSIVE POUR LES NOUVEAUX ÉLÉMENTS ===== */
@media (max-width: 768px) {
    .nav-tabs {
        padding: 0 1rem;
        overflow-x: auto;
        white-space: nowrap;
    }

    .nav-tab {
        padding: 1rem 1.5rem;
        flex-shrink: 0;
    }

    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
        padding: 0 1rem;
    }

    .section-actions {
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
    }

    .filter-row {
        flex-direction: column;
        align-items: stretch;
    }

    .stats-row {
        grid-template-columns: 1fr;
    }

    .stats-grid.large {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .articles-stats,
    .main-stats,
    .detailed-stats {
        padding: 0 1rem;
    }

    .articles-filters {
        padding: 1rem;
        margin: 0 1rem 2rem 1rem;
    }
}

@media (max-width: 480px) {
    .stats-grid.large {
        grid-template-columns: 1fr;
    }

    .month-stat,
    .top-item,
    .supplier-item,
    .trend-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .section-actions {
        flex-direction: column;
    }
}

/* ===== ANIMATION DE CHARGEMENT ===== */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== OPTIMISATIONS PERFORMANCE ===== */
.tab-section {
    will-change: opacity, visibility;
    contain: layout style paint;
}

.nav-tab {
    will-change: background-color, color;
}

/* Accélération matérielle pour les transitions */
.tab-section,
.nav-tab {
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* ===== BADGES DE STOCK ===== */
.stock-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    text-align: center;
    min-width: 60px;
    display: inline-block;
}

.stock-badge.in-stock {
    background-color: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.stock-badge.out-of-stock {
    background-color: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

/* ===== STYLES POUR LES ACTIONS ===== */
.actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    border-radius: 6px;
}

/* ===== RESPONSIVE POUR LES BADGES ===== */
@media (max-width: 480px) {
    .stock-badge {
        min-width: 50px;
        padding: 0.2rem 0.5rem;
        font-size: 0.8rem;
    }

    .actions {
        flex-direction: column;
        gap: 0.25rem;
    }

    .btn-small {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }
}

/* ===== MODULE DE GROUPAGE BL ===== */
.groupage-step {
    background: white;
    margin: 2rem;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    border: 1px solid #e5e7eb;
    margin-bottom: 2rem;
}

.step-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f1f5f9;
}

.step-header h3 {
    font-size: 1.4rem;
    color: #1e293b;
    font-weight: 600;
    margin: 0;
}

.step-actions {
    display: flex;
    gap: 1rem;
}

.btn-outline {
    background: transparent;
    border: 2px solid #3b82f6;
    color: #3b82f6;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-outline:hover {
    background: #3b82f6;
    color: white;
}

/* ===== FILTRES DE GROUPAGE ===== */
.groupage-filters {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    border: 1px solid #e2e8f0;
}

.filter-row {
    display: flex;
    gap: 2rem;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 200px;
}

.filter-group label {
    font-weight: 500;
    color: #374151;
    font-size: 0.9rem;
}

.filter-input {
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.filter-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* ===== SÉLECTION DES BL ===== */
.bl-selection-container {
    margin-top: 1.5rem;
}

.selection-stats {
    background: #eff6ff;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-weight: 500;
    color: #1e40af;
    border: 1px solid #bfdbfe;
}

.bl-selection-table {
    width: 100%;
}

.bl-selection-table th,
.bl-selection-table td {
    text-align: left;
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.bl-selection-table th {
    background: #f8fafc;
    font-weight: 600;
    color: #374151;
}

.bl-selection-table tbody tr:hover {
    background: #f8fafc;
}

.bl-selection-table input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

/* ===== CONFIGURATION BL GROUPÉ ===== */
.grouped-bl-config {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.config-row {
    display: flex;
    gap: 2rem;
    align-items: end;
}

.config-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex: 1;
}

.config-group.full-width {
    flex: 1 1 100%;
}

.config-group label {
    font-weight: 500;
    color: #374151;
    font-size: 0.9rem;
}

.form-input {
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

textarea.form-input {
    resize: vertical;
    min-height: 80px;
}

/* ===== APERÇU DU GROUPAGE ===== */
.groupage-summary {
    margin-bottom: 2rem;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.summary-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.summary-card:nth-child(2) {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.summary-card:nth-child(3) {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.summary-card:nth-child(4) {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.summary-icon {
    font-size: 2rem;
    opacity: 0.9;
}

.summary-content {
    flex: 1;
}

.summary-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.summary-label {
    font-size: 0.9rem;
    opacity: 0.9;
    font-weight: 500;
}

/* ===== ARTICLES GROUPÉS ===== */
.grouped-articles-preview {
    margin-bottom: 2rem;
}

.grouped-articles-preview h4 {
    font-size: 1.2rem;
    color: #1e293b;
    margin-bottom: 1rem;
    font-weight: 600;
}

/* ===== BL SOURCES ===== */
.source-bl-list {
    margin-top: 2rem;
}

.source-bl-list h4 {
    font-size: 1.2rem;
    color: #1e293b;
    margin-bottom: 1rem;
    font-weight: 600;
}

.source-bl-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
}

.source-bl-item {
    background: #f8fafc;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.source-bl-header {
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.source-bl-details {
    font-size: 0.9rem;
    color: #64748b;
    line-height: 1.4;
}

/* ===== RESPONSIVE GROUPAGE ===== */
@media (max-width: 768px) {
    .groupage-step {
        margin: 1rem;
        padding: 1.5rem;
    }

    .step-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .step-actions {
        width: 100%;
        justify-content: flex-start;
    }

    .filter-row {
        flex-direction: column;
        gap: 1rem;
    }

    .filter-group {
        min-width: auto;
        width: 100%;
    }

    .config-row {
        flex-direction: column;
        gap: 1rem;
    }

    .summary-cards {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }

    .summary-card {
        padding: 1rem;
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .summary-icon {
        font-size: 1.5rem;
    }

    .summary-value {
        font-size: 1.5rem;
    }

    .source-bl-container {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .bl-selection-table {
        font-size: 0.8rem;
    }

    .bl-selection-table th,
    .bl-selection-table td {
        padding: 0.5rem;
    }

    .summary-cards {
        grid-template-columns: 1fr;
    }

    .step-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .btn-outline {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}

/* ===== MODULE QR SCANNER ===== */
.qr-scanner-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin: 2rem;
}

/* ===== SCANNER CAMÉRA ===== */
.camera-scanner-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    border: 1px solid #e5e7eb;
}

.scanner-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f1f5f9;
}

.scanner-header h3 {
    font-size: 1.3rem;
    color: #1e293b;
    font-weight: 600;
    margin: 0;
}

.scanner-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.camera-select {
    padding: 0.5rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.9rem;
    min-width: 200px;
}

.camera-container {
    position: relative;
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
    border-radius: 12px;
    overflow: hidden;
    background: #000;
}

.qr-video {
    width: 100%;
    height: auto;
    display: block;
    border-radius: 12px;
    background: #000;
    min-height: 300px;
    object-fit: cover;
}

.qr-video:not([src]) {
    background: #1a1a1a;
    display: flex;
    align-items: center;
    justify-content: center;
}

.qr-video:not([src])::before {
    content: "📹 Caméra en attente...";
    color: #fff;
    font-size: 1.2rem;
    text-align: center;
}

.qr-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* ===== OVERLAY DE SCAN ===== */
.scan-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: rgba(0,0,0,0.3);
    pointer-events: none;
}

.scan-frame {
    position: relative;
    width: 250px;
    height: 250px;
    border: 2px solid rgba(255,255,255,0.5);
    border-radius: 12px;
}

.scan-corners {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.corner {
    position: absolute;
    width: 30px;
    height: 30px;
    border: 3px solid #00ff00;
}

.corner.top-left {
    top: -3px;
    left: -3px;
    border-right: none;
    border-bottom: none;
    border-top-left-radius: 12px;
}

.corner.top-right {
    top: -3px;
    right: -3px;
    border-left: none;
    border-bottom: none;
    border-top-right-radius: 12px;
}

.corner.bottom-left {
    bottom: -3px;
    left: -3px;
    border-right: none;
    border-top: none;
    border-bottom-left-radius: 12px;
}

.corner.bottom-right {
    bottom: -3px;
    right: -3px;
    border-left: none;
    border-top: none;
    border-bottom-right-radius: 12px;
}

.scan-line {
    position: absolute;
    top: 50%;
    left: 10px;
    right: 10px;
    height: 2px;
    background: linear-gradient(90deg, transparent, #00ff00, transparent);
    animation: scanLine 2s ease-in-out infinite;
}

@keyframes scanLine {
    0%, 100% { transform: translateY(-125px); opacity: 0; }
    50% { transform: translateY(0); opacity: 1; }
}

.scan-instructions {
    margin-top: 2rem;
    text-align: center;
    color: white;
}

.scan-instructions p {
    margin: 0.5rem 0;
    font-weight: 500;
    text-shadow: 0 1px 3px rgba(0,0,0,0.5);
}

.scan-status {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* ===== STATUT CAMÉRA ===== */
.camera-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1.5rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #ef4444;
    animation: pulse 2s infinite;
}

.status-dot.active {
    background: #10b981;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.status-text {
    font-weight: 500;
    color: #374151;
}

.scan-stats {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: #64748b;
}

/* ===== UPLOAD D'IMAGE ===== */
.image-upload-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    border: 1px solid #e5e7eb;
}

.upload-header h3 {
    font-size: 1.3rem;
    color: #1e293b;
    font-weight: 600;
    margin: 0 0 1.5rem 0;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f1f5f9;
}

.upload-zone {
    border: 2px dashed #d1d5db;
    border-radius: 12px;
    padding: 3rem 2rem;
    text-align: center;
    background: #fafbfc;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-zone:hover {
    border-color: #3b82f6;
    background: #eff6ff;
}

.upload-zone.dragover {
    border-color: #10b981;
    background: #ecfdf5;
    transform: scale(1.02);
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.upload-icon {
    font-size: 3rem;
    opacity: 0.7;
}

.upload-btn {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.3s ease;
}

.upload-btn:hover {
    background: #2563eb;
}

.uploaded-image-preview {
    text-align: center;
    margin-top: 1.5rem;
}

.uploaded-image-preview img {
    max-width: 100%;
    max-height: 300px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    margin-bottom: 1rem;
}

.image-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* ===== RÉSULTATS DU SCAN ===== */
.scan-results-section {
    background: white;
    margin: 2rem;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    border: 1px solid #e5e7eb;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f1f5f9;
}

.results-header h3 {
    font-size: 1.4rem;
    color: #1e293b;
    font-weight: 600;
    margin: 0;
}

.results-actions {
    display: flex;
    gap: 1rem;
}

/* ===== DONNÉES BRUTES QR ===== */
.qr-raw-data {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.qr-raw-data h4 {
    font-size: 1.1rem;
    color: #374151;
    margin: 0 0 1rem 0;
    font-weight: 600;
}

.raw-data-container {
    position: relative;
}

.raw-data-container pre {
    background: #1f2937;
    color: #f9fafb;
    padding: 1rem;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
    overflow-x: auto;
    margin: 0;
    white-space: pre-wrap;
    word-break: break-all;
}

.raw-data-container button {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
}

/* ===== DONNÉES INTERPRÉTÉES ===== */
.qr-parsed-data {
    margin-bottom: 2rem;
}

.qr-parsed-data h4 {
    font-size: 1.2rem;
    color: #1e293b;
    margin: 0 0 1.5rem 0;
    font-weight: 600;
}

.bl-info-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.bl-header-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.bl-header-info > div {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.bl-header-info label {
    font-size: 0.9rem;
    opacity: 0.9;
    font-weight: 500;
}

.bl-header-info span {
    font-size: 1.1rem;
    font-weight: 600;
}

/* ===== ARTICLES DÉTECTÉS ===== */
.bl-articles-section {
    margin-bottom: 2rem;
}

.bl-articles-section h5 {
    font-size: 1.1rem;
    color: #374151;
    margin: 0 0 1rem 0;
    font-weight: 600;
}

/* ===== VALIDATION ET CORRECTIONS ===== */
.bl-validation-section {
    background: #fffbeb;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #fbbf24;
    margin-bottom: 2rem;
}

.bl-validation-section h5 {
    font-size: 1.1rem;
    color: #92400e;
    margin: 0 0 1rem 0;
    font-weight: 600;
}

.validation-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-row {
    display: flex;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex: 1;
}

.form-group label {
    font-weight: 500;
    color: #374151;
    font-size: 0.9rem;
}

/* ===== HISTORIQUE DES SCANS ===== */
.scan-history {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 2px solid #f1f5f9;
}

.scan-history h4 {
    font-size: 1.2rem;
    color: #1e293b;
    margin: 0 0 1rem 0;
    font-weight: 600;
}

.history-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.history-item {
    background: #f8fafc;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.history-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.history-bl-number {
    font-weight: 600;
    color: #1e293b;
}

.history-timestamp {
    font-size: 0.8rem;
    color: #64748b;
}

.history-actions {
    display: flex;
    gap: 0.5rem;
}

/* ===== GÉNÉRATEUR QR DÉMO ===== */
.demo-qr-section {
    background: white;
    margin: 2rem;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    border: 1px solid #e5e7eb;
}

.demo-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f1f5f9;
}

.demo-header h3 {
    font-size: 1.3rem;
    color: #1e293b;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
}

.demo-header p {
    color: #64748b;
    margin: 0;
}

.demo-form {
    margin-bottom: 2rem;
}

.demo-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.generated-qr-display {
    text-align: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 2px solid #f1f5f9;
}

.generated-qr-display h4 {
    font-size: 1.2rem;
    color: #1e293b;
    margin: 0 0 1.5rem 0;
    font-weight: 600;
}

.qr-display-container {
    display: flex;
    gap: 2rem;
    align-items: center;
    justify-content: center;
}

.qr-info {
    text-align: left;
    max-width: 300px;
}

.qr-info p {
    margin: 0.5rem 0;
    color: #64748b;
    line-height: 1.5;
}

/* ===== RESPONSIVE QR SCANNER ===== */
@media (max-width: 768px) {
    .qr-scanner-container {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        margin: 1rem;
    }

    .camera-scanner-section,
    .image-upload-section {
        padding: 1.5rem;
    }

    .scanner-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .scanner-controls {
        width: 100%;
        justify-content: flex-start;
    }

    .camera-select {
        min-width: auto;
        flex: 1;
    }

    .scan-frame {
        width: 200px;
        height: 200px;
    }

    .bl-header-info {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .form-row {
        flex-direction: column;
    }

    .qr-display-container {
        flex-direction: column;
        gap: 1rem;
    }

    .results-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .results-actions {
        width: 100%;
        justify-content: flex-start;
    }
}

@media (max-width: 480px) {
    .scan-results-section {
        margin: 1rem;
        padding: 1.5rem;
    }

    .upload-zone {
        padding: 2rem 1rem;
    }

    .upload-icon {
        font-size: 2rem;
    }

    .scan-frame {
        width: 150px;
        height: 150px;
    }

    .corner {
        width: 20px;
        height: 20px;
    }

    .demo-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .camera-status {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .scan-stats {
        flex-direction: column;
        gap: 0.5rem;
    }
}

/* ===== BADGES DE STATUT ===== */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
    display: inline-block;
}

.status-badge.valid {
    background-color: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.status-badge.success {
    background-color: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.status-badge.error {
    background-color: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.status-badge.warning {
    background-color: #fffbeb;
    color: #d97706;
    border: 1px solid #fed7aa;
}
