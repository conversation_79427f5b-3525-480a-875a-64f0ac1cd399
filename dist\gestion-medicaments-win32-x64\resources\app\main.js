const { app, BrowserWindow, Menu, dialog, shell, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');
const XLSX = require('xlsx');
const JsonDatabase = require('./json-database');

// Garder une référence globale de l'objet window
let mainWindow;
let database;

function createWindow() {
  // Créer la fenêtre du navigateur
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 800,
    minHeight: 600,
    icon: path.join(__dirname, 'assets', 'icon.png'),
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    show: false, // Ne pas afficher immédiatement
    titleBarStyle: 'default'
  });

  // Charger le fichier index.html de l'application
  mainWindow.loadFile('index.html');

  // Afficher la fenêtre quand elle est prête
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();

    // Optionnel : ouvrir les DevTools en mode développement
    // mainWindow.webContents.openDevTools();
  });

  // Émis quand la fenêtre est fermée
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Créer le menu de l'application
  createMenu();
}

function createMenu() {
  const template = [
    {
      label: 'Fichier',
      submenu: [
        {
          label: 'Nouveau Bon',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow.webContents.executeJavaScript('showForm()');
          }
        },
        {
          label: 'Exporter',
          accelerator: 'CmdOrCtrl+E',
          click: () => {
            mainWindow.webContents.executeJavaScript('exportData()');
          }
        },
        {
          label: 'Importer Articles Excel',
          accelerator: 'CmdOrCtrl+I',
          click: () => {
            importExcelFile();
          }
        },
        { type: 'separator' },
        {
          label: 'Quitter',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'Édition',
      submenu: [
        { label: 'Annuler', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
        { label: 'Rétablir', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
        { type: 'separator' },
        { label: 'Couper', accelerator: 'CmdOrCtrl+X', role: 'cut' },
        { label: 'Copier', accelerator: 'CmdOrCtrl+C', role: 'copy' },
        { label: 'Coller', accelerator: 'CmdOrCtrl+V', role: 'paste' }
      ]
    },
    {
      label: 'Affichage',
      submenu: [
        { label: 'Recharger', accelerator: 'CmdOrCtrl+R', role: 'reload' },
        { label: 'Forcer le rechargement', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
        { label: 'Outils de développement', accelerator: 'F12', role: 'toggleDevTools' },
        { type: 'separator' },
        { label: 'Zoom avant', accelerator: 'CmdOrCtrl+Plus', role: 'zoomIn' },
        { label: 'Zoom arrière', accelerator: 'CmdOrCtrl+-', role: 'zoomOut' },
        { label: 'Zoom par défaut', accelerator: 'CmdOrCtrl+0', role: 'resetZoom' },
        { type: 'separator' },
        { label: 'Plein écran', accelerator: 'F11', role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Aide',
      submenu: [
        {
          label: 'À propos',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'À propos',
              message: 'Gestion des Médicaments',
              detail: 'Application desktop pour la gestion des médicaments dans les bons de livraison.\\n\\nVersion 1.0.0\\n\\nDéveloppé avec Electron'
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// Fonction d'import Excel
function importExcelFile() {
  dialog.showOpenDialog(mainWindow, {
    title: 'Importer des articles depuis Excel',
    filters: [
      { name: 'Fichiers Excel', extensions: ['xlsx', 'xls', 'csv'] }
    ],
    properties: ['openFile']
  }).then(result => {
    if (!result.canceled && result.filePaths.length > 0) {
      try {
        const filePath = result.filePaths[0];
        const workbook = XLSX.readFile(filePath);
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet);

        // Mapper les colonnes Excel vers notre structure simplifiée
        const articles = jsonData.map(row => ({
          designation: row['Désignation'] || row['designation'] || row['DESIGNATION'] ||
                      row['Nom'] || row['nom'] || row['NOM'] ||
                      row['Article'] || row['article'] || row['ARTICLE'] || '',
          prix_unitaire: parseFloat(row['Prix'] || row['prix'] || row['PRIX'] || row['Prix Unitaire'] || 0),
          stock_actuel: parseInt(row['Stock'] || row['stock'] || row['STOCK'] || row['Stock Actuel'] || 0)
        })).filter(article => article.designation && article.designation.trim()); // Filtrer les lignes vides

        if (articles.length > 0) {
          database.importArticlesFromExcel(articles);
          dialog.showMessageBox(mainWindow, {
            type: 'info',
            title: 'Import réussi',
            message: `${articles.length} articles ont été importés avec succès !`,
            detail: 'Les articles sont maintenant disponibles dans la base de données.'
          });

          // Rafraîchir l'interface
          mainWindow.webContents.send('refresh-data');
        } else {
          dialog.showErrorBox('Erreur d\'import', 'Aucun article valide trouvé dans le fichier.');
        }
      } catch (error) {
        console.error('Erreur lors de l\'import:', error);
        dialog.showErrorBox('Erreur d\'import', `Erreur lors de la lecture du fichier: ${error.message}`);
      }
    }
  });
}

// Cette méthode sera appelée quand Electron aura fini de s'initialiser
app.whenReady().then(() => {
  // Initialiser la base de données
  database = new JsonDatabase();

  // Configurer les gestionnaires IPC
  setupIpcHandlers();

  // Créer la fenêtre
  createWindow();
});

// Quitter quand toutes les fenêtres sont fermées
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (mainWindow === null) {
    createWindow();
  }
});

// Empêcher la navigation vers des sites externes
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
    shell.openExternal(navigationUrl);
  });
});

// Gestion des erreurs non capturées
process.on('uncaughtException', (error) => {
  console.error('Erreur non capturée:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Promesse rejetée non gérée:', reason);
});

// Configuration des gestionnaires IPC
function setupIpcHandlers() {
  // Articles
  ipcMain.handle('get-all-articles', () => database.getAllArticles());
  ipcMain.handle('get-article-by-id', (event, id) => database.getArticleById(id));
  ipcMain.handle('search-articles', (event, searchTerm) => database.searchArticles(searchTerm));
  ipcMain.handle('add-article', (event, article) => database.addArticle(article));
  ipcMain.handle('update-article', (event, id, article) => database.updateArticle(id, article));
  ipcMain.handle('delete-article', (event, id) => database.deleteArticle(id));

  // Bons de livraison
  ipcMain.handle('get-all-bons', () => database.getAllBons());
  ipcMain.handle('get-bon-with-lignes', (event, id) => database.getBonWithLignes(id));
  ipcMain.handle('add-bon', (event, bon) => database.addBon(bon));
  ipcMain.handle('update-bon', (event, id, bon) => database.updateBon(id, bon));
  ipcMain.handle('delete-bon', (event, id) => database.deleteBon(id));

  // Lignes de livraison
  ipcMain.handle('add-ligne-livraison', (event, ligne) => database.addLigneLivraison(ligne));
  ipcMain.handle('update-ligne-livraison', (event, id, ligne) => database.updateLigneLivraison(id, ligne));
  ipcMain.handle('delete-ligne-livraison', (event, id) => database.deleteLigneLivraison(id));

  // Statistiques et export
  ipcMain.handle('get-statistiques', () => database.getStatistiques());
  ipcMain.handle('export-to-csv', () => database.exportToCSV());
}
