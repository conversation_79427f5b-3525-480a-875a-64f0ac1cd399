/* ===== STYLES POUR LE MENU DE NAVIGATION ET MODULES ===== */

/* Menu de Navigation */
.app-nav {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-tabs {
    display: flex;
    gap: 0;
}

.nav-tab {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    padding: 15px 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.95rem;
    font-weight: 500;
    border-bottom: 3px solid transparent;
    position: relative;
    overflow: hidden;
}

.nav-tab:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.nav-tab.active {
    color: white;
    background: rgba(255, 255, 255, 0.15);
    border-bottom-color: #ffd700;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.nav-tab .icon {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.nav-tab:hover .icon {
    transform: scale(1.1);
}

.nav-tab.active .icon {
    transform: scale(1.15);
}

/* Animation de transition */
.nav-tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.nav-tab:hover::before {
    left: 100%;
}

/* Responsive pour le menu */
@media (max-width: 768px) {
    .nav-tabs {
        flex-direction: column;
    }

    .nav-tab {
        justify-content: center;
        padding: 12px 20px;
    }

    .nav-tab span {
        font-size: 0.9rem;
    }
}

/* ===== SECTION GESTION ARTICLES ===== */
.gestion-articles-section {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: white;
    z-index: 1000;
    overflow-y: auto;
    padding: 20px;
}

.gestion-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.gestion-header h2 {
    margin: 0;
    color: #2c3e50;
    font-size: 2rem;
}

.gestion-controls {
    display: flex;
    align-items: center;
    gap: 20px;
}

.search-articles {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.search-articles input {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.9rem;
    min-width: 250px;
}

.articles-container {
    margin-bottom: 30px;
}

/* Tableau des articles */
.gestion-articles-section .table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.gestion-articles-section .data-table {
    width: 100%;
    border-collapse: collapse;
}

.gestion-articles-section .data-table th {
    background: #343a40;
    color: white;
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.gestion-articles-section .data-table td {
    padding: 12px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.gestion-articles-section .data-table tr:hover {
    background: #f8f9fa;
}

.gestion-articles-section .data-table tr:nth-child(even) {
    background: #f8f9fa;
}

.gestion-articles-section .data-table tr:nth-child(even):hover {
    background: #e9ecef;
}

/* Colonnes spécifiques pour les articles */
.article-designation {
    font-weight: 600;
    color: #2c3e50;
    max-width: 400px;
    word-wrap: break-word;
}

.article-prix {
    text-align: right;
    font-weight: 600;
    color: #dc3545;
}

.article-stock {
    text-align: center;
    font-weight: 600;
    color: #28a745;
}

.article-date {
    color: #6c757d;
    font-size: 0.9rem;
}

.article-actions {
    text-align: center;
}

.article-actions .btn {
    margin: 0 2px;
    padding: 5px 10px;
    font-size: 0.8rem;
}

/* État vide pour les articles */
.gestion-articles-section .empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.gestion-articles-section .empty-state .empty-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.gestion-articles-section .empty-state h3 {
    margin-bottom: 10px;
    color: #495057;
}

/* ===== STYLES GÉNÉRAUX ===== */

/* Classe pour masquer les sections */
.hidden {
    display: none !important;
}

/* S'assurer que la section principale est visible par défaut */
.app-main {
    display: block;
}

/* Espacement entre les sections */
.statistiques-section,
.gestion-articles-section {
    margin-top: 20px;
    border-top: 1px solid #e9ecef;
    padding-top: 20px;
}

/* ===== STYLES POUR LE MENU DE NAVIGATION ===== */

/* Menu de Navigation */
.app-nav {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-tabs {
    display: flex;
    gap: 0;
}

.nav-tab {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    padding: 15px 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.95rem;
    font-weight: 500;
    border-bottom: 3px solid transparent;
    position: relative;
    overflow: hidden;
}

.nav-tab:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.nav-tab.active {
    color: white;
    background: rgba(255, 255, 255, 0.15);
    border-bottom-color: #ffd700;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.nav-tab .icon {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.nav-tab:hover .icon {
    transform: scale(1.1);
}

.nav-tab.active .icon {
    transform: scale(1.15);
}

/* Responsive pour le menu */
@media (max-width: 768px) {
    .nav-tabs {
        flex-direction: column;
    }

    .nav-tab {
        justify-content: center;
        padding: 12px 20px;
    }

    .nav-tab span {
        font-size: 0.9rem;
    }
}

/* ===== SECTION GESTION ARTICLES ===== */
.gestion-articles-section {
    width: 100%;
    background: white;
    padding: 20px;
    min-height: 500px;
}

.gestion-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.gestion-header h2 {
    margin: 0;
    color: #2c3e50;
    font-size: 2rem;
}

.gestion-controls {
    display: flex;
    align-items: center;
    gap: 20px;
}

.search-articles {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.search-articles input {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.9rem;
    min-width: 250px;
}

/* ===== STYLES POUR LE MODULE STATISTIQUES ARTICLES ===== */

/* Section Statistiques Articles */
.statistiques-section {
    width: 100%;
    background: white;
    padding: 20px;
    min-height: 500px;
}

.statistiques-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.statistiques-header h2 {
    margin: 0;
    color: #2c3e50;
    font-size: 2rem;
}

.statistiques-controls {
    display: flex;
    align-items: center;
    gap: 20px;
}

.periode-filter {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.periode-filter label {
    font-weight: 600;
    color: #495057;
}

.periode-filter input[type="date"] {
    padding: 5px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.9rem;
}

.periode-filter span {
    color: #6c757d;
    font-weight: 500;
}

.stats-resume {
    margin-bottom: 30px;
}

.stats-resume .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stats-resume .stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    transition: transform 0.3s ease;
}

.stats-resume .stat-card:hover {
    transform: translateY(-5px);
}

.stats-resume .stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 8px;
}

.stats-resume .stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Tableau des statistiques */
.statistiques-section .table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.statistiques-section .data-table {
    width: 100%;
    border-collapse: collapse;
}

.statistiques-section .data-table th {
    background: #343a40;
    color: white;
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.statistiques-section .data-table td {
    padding: 12px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.statistiques-section .data-table tr:hover {
    background: #f8f9fa;
}

.statistiques-section .data-table tr:nth-child(even) {
    background: #f8f9fa;
}

.statistiques-section .data-table tr:nth-child(even):hover {
    background: #e9ecef;
}

/* Colonnes spécifiques */
.stats-article-nom {
    font-weight: 600;
    color: #2c3e50;
    max-width: 300px;
    word-wrap: break-word;
}

.stats-quantite {
    text-align: center;
    font-weight: 600;
    color: #28a745;
}

.stats-montant {
    text-align: right;
    font-weight: 600;
    color: #dc3545;
}

.stats-prix-moyen {
    text-align: right;
    color: #6c757d;
}

.stats-nombre-bons {
    text-align: center;
    color: #007bff;
}

.stats-date {
    color: #6c757d;
    font-size: 0.9rem;
}

.stats-bons-list {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #6c757d;
    font-size: 0.85rem;
}

.stats-bons-list:hover {
    overflow: visible;
    white-space: normal;
    background: #fff3cd;
    padding: 5px;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    position: relative;
    z-index: 10;
}

/* État vide pour les statistiques */
.statistiques-section .empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.statistiques-section .empty-state .empty-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.statistiques-section .empty-state h3 {
    margin-bottom: 10px;
    color: #495057;
}

/* Responsive */
@media (max-width: 768px) {
    .statistiques-header {
        flex-direction: column;
        gap: 20px;
        align-items: stretch;
    }

    .statistiques-controls {
        flex-direction: column;
        gap: 15px;
    }

    .periode-filter {
        flex-direction: column;
        gap: 10px;
    }

    .stats-resume .stats-grid {
        grid-template-columns: 1fr;
    }

    .statistiques-section .data-table {
        font-size: 0.8rem;
    }

    .statistiques-section .data-table th,
    .statistiques-section .data-table td {
        padding: 8px 6px;
    }
}
