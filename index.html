<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Médicaments - Bons de Livraison</title>
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval'; script-src 'self' 'unsafe-inline' 'unsafe-eval';">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="app">
        <header class="app-header">
            <h1>🏥 Gestion des Médicaments - Bons de Livraison</h1>
            <div class="header-actions">
                <button id="btn-import-articles" class="btn btn-info">📁 Importer Articles</button>
                <button id="btn-nouveau" class="btn btn-primary">+ Nouveau Bon</button>
                <button id="btn-export" class="btn btn-secondary">📊 Exporter</button>
            </div>
        </header>

        <main class="app-main">
            <!-- Formulaire de création/modification -->
            <div id="form-section" class="form-section hidden">
                <div class="form-container">
                    <h2 id="form-title">Nouveau Bon de Livraison</h2>
                    <form id="bon-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="numeroBL">Numéro BL</label>
                                <input type="text" id="numeroBL" required>
                            </div>
                            <div class="form-group">
                                <label for="dateBL">Date BL</label>
                                <input type="date" id="dateBL" required>
                            </div>
                            <div class="form-group">
                                <label for="fournisseur">Fournisseur</label>
                                <input type="text" id="fournisseur" required>
                            </div>
                        </div>

                        <!-- Section médicaments -->
                        <div class="medicaments-section">
                            <h3>Ajouter un médicament</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="article">Article</label>
                                    <input type="text" id="article" placeholder="Nom du médicament">
                                    <div id="suggestions-dropdown" class="suggestions-dropdown"></div>
                                </div>
                                <div class="form-group">
                                    <label for="quantite">Quantité</label>
                                    <input type="number" id="quantite" min="1" step="1">
                                </div>
                                <div class="form-group">
                                    <label for="prixUnitaire">Prix unitaire (DT)</label>
                                    <input type="number" id="prixUnitaire" min="0" step="0.001">
                                </div>
                            </div>
                            <button type="button" id="btn-add-medicament" class="btn btn-secondary">+ Ajouter</button>

                            <!-- Liste des médicaments ajoutés -->
                            <div id="medicaments-list" class="medicaments-list"></div>
                        </div>

                        <div class="form-actions">
                            <button type="button" id="btn-cancel" class="btn btn-secondary">Annuler</button>
                            <button type="submit" class="btn btn-primary">Enregistrer</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Tableau des bons de livraison -->
            <div class="table-section">
                <div class="table-header">
                    <h2>Liste des Bons de Livraison</h2>
                    <div class="search-container">
                        <input type="text" id="search-input" placeholder="Rechercher..." class="search-input">
                    </div>
                </div>

                <div class="table-container">
                    <table id="bons-table" class="data-table">
                        <thead>
                            <tr>
                                <th>Numéro BL</th>
                                <th>Date BL</th>
                                <th>Montant BL</th>
                                <th>Article</th>
                                <th>Quantité</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="table-body">
                            <!-- Les données seront ajoutées dynamiquement -->
                        </tbody>
                    </table>
                </div>

                <div id="empty-state" class="empty-state">
                    <div class="empty-icon">📋</div>
                    <h3>Aucun bon de livraison</h3>
                    <p>Cliquez sur "Nouveau Bon" pour commencer</p>
                </div>
            </div>

            <!-- Statistiques -->
            <div class="stats-section">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" id="total-bons">0</div>
                        <div class="stat-label">Total Bons</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="total-montant">0 DT</div>
                        <div class="stat-label">Montant Total</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="total-quantite">0</div>
                        <div class="stat-label">Quantité Totale</div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html>
