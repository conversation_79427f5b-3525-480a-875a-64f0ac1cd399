// Données de l'application
let bonsLivraison = [];
let articles = [];
let currentEditingId = null;
let medicamentsTemp = [];
let currentBonId = null;

// Éléments DOM
const formSection = document.getElementById('form-section');
const bonForm = document.getElementById('bon-form');
const tableBody = document.getElementById('table-body');
const emptyState = document.getElementById('empty-state');
const searchInput = document.getElementById('search-input');

// Boutons
const btnNouveau = document.getElementById('btn-nouveau');
const btnCancel = document.getElementById('btn-cancel');
const btnAddMedicament = document.getElementById('btn-add-medicament');
const btnExport = document.getElementById('btn-export');
const btnImportArticles = document.getElementById('btn-import-articles');

// Champs du formulaire
const numeroBLInput = document.getElementById('numeroBL');
const dateBLInput = document.getElementById('dateBL');
const fournisseurInput = document.getElementById('fournisseur');
const articleInput = document.getElementById('article');
const quantiteInput = document.getElementById('quantite');
const prixUnitaireInput = document.getElementById('prixUnitaire');
const medicamentsList = document.getElementById('medicaments-list');
const formTitle = document.getElementById('form-title');

// Statistiques - CORRIGÉ : Seulement 3 éléments
const totalBonsEl = document.getElementById('total-bons');
const totalMontantEl = document.getElementById('total-montant');
const totalQuantiteEl = document.getElementById('total-quantite');

// Initialisation
document.addEventListener('DOMContentLoaded', async function() {
    console.log('DOM chargé, initialisation...');

    // Attendre un peu pour s'assurer que tous les éléments sont chargés
    await new Promise(resolve => setTimeout(resolve, 100));

    // Vérifier que tous les éléments DOM sont présents
    const requiredElements = {
        'btn-nouveau': btnNouveau,
        'btn-add-medicament': btnAddMedicament,
        'form-section': formSection,
        'table-body': tableBody,
        'total-bons': totalBonsEl,
        'total-montant': totalMontantEl,
        'total-quantite': totalQuantiteEl
    };

    let missingElements = [];
    for (const [name, element] of Object.entries(requiredElements)) {
        if (!element) {
            missingElements.push(name);
        }
    }

    if (missingElements.length > 0) {
        console.error('Éléments DOM manquants:', missingElements);
        alert('Erreur: Certains éléments de l\'interface sont manquants. Rechargez la page.');
        return;
    }

    console.log('Tous les éléments DOM sont présents');

    // Initialiser les gestionnaires d'événements
    initializeEventListeners();

    // Charger les données
    await loadData();

    // Mettre à jour l'affichage
    updateDisplay();

    console.log('Initialisation terminée');
});

// Initialiser les gestionnaires d'événements
function initializeEventListeners() {
    console.log('Initialisation des gestionnaires d\'événements...');

    // Bouton nouveau bon
    if (btnNouveau) {
        btnNouveau.addEventListener('click', showForm);
    }

    // Bouton annuler
    if (btnCancel) {
        btnCancel.addEventListener('click', hideForm);
    }

    // Bouton ajouter médicament
    if (btnAddMedicament) {
        btnAddMedicament.addEventListener('click', addMedicament);
    }

    // Formulaire
    if (bonForm) {
        bonForm.addEventListener('submit', saveBon);
    }

    // Recherche
    if (searchInput) {
        searchInput.addEventListener('input', filterTable);
    }

    // Export
    if (btnExport) {
        btnExport.addEventListener('click', exportData);
    }

    // Import articles
    if (btnImportArticles) {
        btnImportArticles.addEventListener('click', importArticles);
    }

    // Suggestions d'articles
    if (articleInput) {
        articleInput.addEventListener('input', showArticleSuggestions);
        articleInput.addEventListener('blur', () => {
            setTimeout(hideArticleSuggestions, 200);
        });
    }

    console.log('Gestionnaires d\'événements initialisés');
}

// Charger les données
async function loadData() {
    console.log('Chargement des données...');
    
    try {
        if (window.electronAPI) {
            // Charger les bons de livraison
            bonsLivraison = await window.electronAPI.getAllBons() || [];
            console.log('Bons de livraison chargés:', bonsLivraison.length);

            // Charger les articles
            articles = await window.electronAPI.getAllArticles() || [];
            console.log('Articles chargés:', articles.length);
        } else {
            console.warn('electronAPI non disponible, utilisation des données locales');
            bonsLivraison = [];
            articles = [];
        }
    } catch (error) {
        console.error('Erreur lors du chargement des données:', error);
        bonsLivraison = [];
        articles = [];
    }
}

// Afficher le formulaire
function showForm(editingBon = null) {
    console.log('Affichage du formulaire');
    
    if (editingBon) {
        currentEditingId = editingBon.id;
        formTitle.textContent = 'Modifier le Bon de Livraison';
        
        // Remplir le formulaire avec les données existantes
        numeroBLInput.value = editingBon.numeroBL || '';
        dateBLInput.value = editingBon.dateBL || '';
        fournisseurInput.value = editingBon.fournisseur || '';
        
        // Charger les médicaments
        medicamentsTemp = editingBon.medicaments || [];
        updateMedicamentsList();
    } else {
        currentEditingId = null;
        formTitle.textContent = 'Nouveau Bon de Livraison';
        bonForm.reset();
        medicamentsTemp = [];
        updateMedicamentsList();
        
        // Générer un numéro de BL automatique
        const nextNumber = generateNextBLNumber();
        numeroBLInput.value = nextNumber;
        
        // Date du jour
        const today = new Date().toISOString().split('T')[0];
        dateBLInput.value = today;
    }
    
    formSection.classList.remove('hidden');
}

// Masquer le formulaire
function hideForm() {
    console.log('Masquage du formulaire');
    formSection.classList.add('hidden');
    currentEditingId = null;
    medicamentsTemp = [];
    bonForm.reset();
}

// Générer le prochain numéro de BL
function generateNextBLNumber() {
    if (bonsLivraison.length === 0) {
        return 'BL-001';
    }
    
    const lastBL = bonsLivraison[bonsLivraison.length - 1];
    const lastNumber = parseInt(lastBL.numeroBL.split('-')[1]) || 0;
    const nextNumber = lastNumber + 1;
    
    return `BL-${nextNumber.toString().padStart(3, '0')}`;
}

// Ajouter un médicament
function addMedicament() {
    console.log('Ajout d\'un médicament');
    
    const article = articleInput.value.trim();
    const quantite = parseInt(quantiteInput.value);
    const prixUnitaire = parseFloat(prixUnitaireInput.value);
    
    if (!article || !quantite || quantite <= 0 || !prixUnitaire || prixUnitaire < 0) {
        alert('Veuillez remplir tous les champs correctement');
        return;
    }
    
    const total = quantite * prixUnitaire;
    
    const medicament = {
        id: Date.now(),
        article: article,
        quantite: quantite,
        prixUnitaire: prixUnitaire,
        total: total
    };
    
    medicamentsTemp.push(medicament);
    updateMedicamentsList();
    
    // Vider les champs
    articleInput.value = '';
    quantiteInput.value = '';
    prixUnitaireInput.value = '';
    
    articleInput.focus();
}

// Mettre à jour la liste des médicaments
function updateMedicamentsList() {
    if (!medicamentsList) return;
    
    medicamentsList.innerHTML = '';
    
    medicamentsTemp.forEach(med => {
        const div = document.createElement('div');
        div.className = 'medicament-item';
        div.innerHTML = `
            <div class="medicament-info">
                <strong>${med.article}</strong><br>
                Quantité: ${med.quantite} | Prix unitaire: ${med.prixUnitaire.toFixed(3)} DT | Total: ${med.total.toFixed(3)} DT
            </div>
            <button type="button" class="btn btn-danger btn-small" onclick="removeMedicament(${med.id})">
                Supprimer
            </button>
        `;
        medicamentsList.appendChild(div);
    });
}

// Supprimer un médicament
function removeMedicament(id) {
    medicamentsTemp = medicamentsTemp.filter(med => med.id !== id);
    updateMedicamentsList();
}

// Sauvegarder le bon
async function saveBon(e) {
    e.preventDefault();
    console.log('Sauvegarde du bon');
    
    const numeroBL = numeroBLInput.value.trim();
    const dateBL = dateBLInput.value;
    const fournisseur = fournisseurInput.value.trim();
    
    if (!numeroBL || !dateBL || !fournisseur) {
        alert('Veuillez remplir tous les champs obligatoires');
        return;
    }
    
    if (medicamentsTemp.length === 0) {
        alert('Veuillez ajouter au moins un médicament');
        return;
    }
    
    // Calculer le montant total
    const montantBL = medicamentsTemp.reduce((sum, med) => sum + med.total, 0);
    
    const bonData = {
        numeroBL: numeroBL,
        dateBL: dateBL,
        fournisseur: fournisseur,
        montantBL: montantBL,
        medicaments: medicamentsTemp
    };
    
    try {
        if (window.electronAPI) {
            if (currentEditingId) {
                // Modification
                await window.electronAPI.updateBon(currentEditingId, bonData);
                console.log('Bon modifié avec succès');
            } else {
                // Création
                const newBon = await window.electronAPI.createBon(bonData);
                console.log('Bon créé avec succès:', newBon);
            }
        } else {
            // Mode local (sans Electron)
            if (currentEditingId) {
                const index = bonsLivraison.findIndex(b => b.id === currentEditingId);
                if (index !== -1) {
                    bonsLivraison[index] = { ...bonData, id: currentEditingId };
                }
            } else {
                bonData.id = Date.now();
                bonsLivraison.push(bonData);
            }
        }
        
        // Recharger les données et mettre à jour l'affichage
        await loadData();
        updateDisplay();
        hideForm();
        
        alert(currentEditingId ? 'Bon modifié avec succès!' : 'Bon créé avec succès!');
        
    } catch (error) {
        console.error('Erreur lors de la sauvegarde:', error);
        alert('Erreur lors de la sauvegarde: ' + error.message);
    }
}

// Mettre à jour l'affichage
function updateDisplay() {
    console.log('Mise à jour de l\'affichage');
    updateTable();
    updateStats();
    updateEmptyState();
}

// Mettre à jour le tableau
function updateTable() {
    if (!tableBody) return;
    
    tableBody.innerHTML = '';
    
    // Créer une ligne par BL (consolidée)
    bonsLivraison.forEach(bon => {
        const row = document.createElement('tr');
        
        // Calculer les totaux pour ce BL
        const totalQuantite = bon.medicaments ? bon.medicaments.reduce((sum, med) => sum + (med.quantite || 0), 0) : 0;
        const articlesCount = bon.medicaments ? bon.medicaments.length : 0;
        const articlesText = articlesCount > 1 ? `${articlesCount} articles` : 
                           (bon.medicaments && bon.medicaments[0] ? bon.medicaments[0].article : 'Aucun article');
        
        row.innerHTML = `
            <td>${bon.numeroBL || 'N/A'}</td>
            <td>${formatDate(bon.dateBL)}</td>
            <td>${(bon.montantBL || 0).toFixed(3)} DT</td>
            <td>${articlesText}</td>
            <td>${totalQuantite}</td>
            <td class="actions">
                <button class="btn btn-edit btn-small" onclick="editBon(${bon.id})">
                    Modifier
                </button>
                <button class="btn btn-danger btn-small" onclick="deleteBon(${bon.id})">
                    Supprimer
                </button>
            </td>
        `;
        
        tableBody.appendChild(row);
    });
}

// Mettre à jour les statistiques - CORRIGÉ pour 3 sections
async function updateStats() {
    console.log('=== MISE À JOUR DES STATISTIQUES ===');
    console.log('Nombre de BL:', bonsLivraison.length);
    
    const totalBons = bonsLivraison.length;
    const totalMontant = bonsLivraison.reduce((sum, bon) => sum + (bon.montantBL || 0), 0);
    
    let totalQuantite = 0;
    
    // Calculer la quantité totale réelle en utilisant les données de la base
    for (let i = 0; i < bonsLivraison.length; i++) {
        const bon = bonsLivraison[i];
        if (!bon) continue;
        
        try {
            // Récupérer les détails réels du BL avec ses lignes
            if (window.electronAPI && window.electronAPI.getBonWithLignes) {
                const bonDetails = await window.electronAPI.getBonWithLignes(bon.id);
                if (bonDetails && bonDetails.lignes) {
                    console.log(`BL ${bon.numeroBL}: ${bonDetails.lignes.length} lignes`);
                    for (let j = 0; j < bonDetails.lignes.length; j++) {
                        const ligne = bonDetails.lignes[j];
                        if (ligne && ligne.quantite_livree) {
                            totalQuantite += ligne.quantite_livree;
                            console.log(`  - Article: ${ligne.article_nom || 'N/A'}, Quantité: ${ligne.quantite_livree}`);
                        }
                    }
                }
            } else {
                // Fallback : utiliser les données locales si l'API n'est pas disponible
                console.log('API non disponible, utilisation des données locales pour BL', bon.numeroBL);
                if (bon.medicaments) {
                    for (let j = 0; j < bon.medicaments.length; j++) {
                        const med = bon.medicaments[j];
                        if (med && med.quantite) {
                            totalQuantite += med.quantite;
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Erreur lors du calcul des quantités pour BL', bon.id, ':', error);
            // Fallback en cas d'erreur
            if (bon.medicaments) {
                for (let j = 0; j < bon.medicaments.length; j++) {
                    const med = bon.medicaments[j];
                    if (med && med.quantite) {
                        totalQuantite += med.quantite;
                    }
                }
            }
        }
    }
    
    console.log('Statistiques calculées:');
    console.log('- Total BL:', totalBons);
    console.log('- Montant total:', totalMontant.toFixed(3), 'DT');
    console.log('- Quantité totale réelle:', totalQuantite);
    
    // Mettre à jour l'affichage - SEULEMENT 3 ÉLÉMENTS
    if (totalBonsEl) {
        totalBonsEl.textContent = totalBons;
        console.log('Mis à jour total-bons:', totalBons);
    }
    if (totalMontantEl) {
        totalMontantEl.textContent = totalMontant.toFixed(3) + ' DT';
        console.log('Mis à jour total-montant:', totalMontant.toFixed(3), 'DT');
    }
    if (totalQuantiteEl) {
        totalQuantiteEl.textContent = totalQuantite;
        console.log('Mis à jour total-quantite:', totalQuantite);
    }
}

// Mettre à jour l'état vide
function updateEmptyState() {
    if (!emptyState) return;
    
    if (bonsLivraison.length === 0) {
        emptyState.classList.remove('hidden');
    } else {
        emptyState.classList.add('hidden');
    }
}

// Formater une date
function formatDate(dateString) {
    if (!dateString) return 'N/A';
    
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString('fr-FR');
    } catch (error) {
        return dateString;
    }
}

// Modifier un bon
function editBon(id) {
    console.log('Modification du bon:', id);
    const bon = bonsLivraison.find(b => b.id === id);
    if (bon) {
        showForm(bon);
    }
}

// Supprimer un bon
async function deleteBon(id) {
    console.log('Suppression du bon:', id);
    
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce bon de livraison ?')) {
        return;
    }
    
    try {
        if (window.electronAPI) {
            await window.electronAPI.deleteBon(id);
        } else {
            bonsLivraison = bonsLivraison.filter(b => b.id !== id);
        }
        
        await loadData();
        updateDisplay();
        alert('Bon supprimé avec succès!');
        
    } catch (error) {
        console.error('Erreur lors de la suppression:', error);
        alert('Erreur lors de la suppression: ' + error.message);
    }
}

// Filtrer le tableau
function filterTable() {
    const searchTerm = searchInput.value.toLowerCase();
    const rows = tableBody.querySelectorAll('tr');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// Exporter les données
function exportData() {
    console.log('Export des données');
    
    if (bonsLivraison.length === 0) {
        alert('Aucune donnée à exporter');
        return;
    }
    
    // Créer les données CSV
    const headers = ['Numéro BL', 'Date BL', 'Fournisseur', 'Article', 'Quantité', 'Prix Unitaire', 'Total'];
    const csvData = [headers];
    
    bonsLivraison.forEach(bon => {
        if (bon.medicaments && bon.medicaments.length > 0) {
            bon.medicaments.forEach(med => {
                csvData.push([
                    bon.numeroBL || '',
                    bon.dateBL || '',
                    bon.fournisseur || '',
                    med.article || '',
                    med.quantite || 0,
                    med.prixUnitaire || 0,
                    med.total || 0
                ]);
            });
        } else {
            csvData.push([
                bon.numeroBL || '',
                bon.dateBL || '',
                bon.fournisseur || '',
                '',
                0,
                0,
                bon.montantBL || 0
            ]);
        }
    });
    
    // Convertir en CSV
    const csvContent = csvData.map(row => row.join(',')).join('\n');
    
    // Télécharger le fichier
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `bons_livraison_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    alert('Données exportées avec succès!');
}

// Importer les articles
function importArticles() {
    console.log('Import des articles');
    
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.xlsx,.xls';
    
    input.onchange = async function(e) {
        const file = e.target.files[0];
        if (!file) return;
        
        try {
            if (window.electronAPI && window.electronAPI.importArticlesFromExcel) {
                const result = await window.electronAPI.importArticlesFromExcel(file.path);
                alert(`Import réussi! ${result.count} articles importés.`);
                
                // Recharger les articles
                articles = await window.electronAPI.getAllArticles() || [];
                console.log('Articles rechargés après import:', articles.length);
            } else {
                alert('Fonction d\'import non disponible');
            }
        } catch (error) {
            console.error('Erreur lors de l\'import:', error);
            alert('Erreur lors de l\'import: ' + error.message);
        }
    };
    
    input.click();
}

// Suggestions d'articles
function showArticleSuggestions() {
    const query = articleInput.value.toLowerCase();
    if (query.length < 2) {
        hideArticleSuggestions();
        return;
    }
    
    const suggestions = articles.filter(article => 
        article.designation && article.designation.toLowerCase().includes(query)
    ).slice(0, 5);
    
    if (suggestions.length === 0) {
        hideArticleSuggestions();
        return;
    }
    
    let dropdown = document.getElementById('suggestions-dropdown');
    if (!dropdown) {
        dropdown = document.createElement('div');
        dropdown.id = 'suggestions-dropdown';
        dropdown.className = 'suggestions-dropdown';
        articleInput.parentNode.appendChild(dropdown);
    }
    
    dropdown.innerHTML = '';
    dropdown.style.display = 'block';
    
    suggestions.forEach(article => {
        const item = document.createElement('div');
        item.className = 'suggestion-item';
        item.innerHTML = `
            <div class="article-name">${article.designation}</div>
            <div class="article-details">Prix: ${article.prix_unitaire ? article.prix_unitaire.toFixed(3) : '0.000'} DT</div>
        `;
        
        item.addEventListener('click', () => {
            articleInput.value = article.designation;
            if (article.prix_unitaire) {
                prixUnitaireInput.value = article.prix_unitaire.toFixed(3);
            }
            hideArticleSuggestions();
            quantiteInput.focus();
        });
        
        dropdown.appendChild(item);
    });
}

// Masquer les suggestions
function hideArticleSuggestions() {
    const dropdown = document.getElementById('suggestions-dropdown');
    if (dropdown) {
        dropdown.style.display = 'none';
    }
}

// Fonction de test des statistiques
function testStatistiques() {
    console.log('=== TEST STATISTIQUES ===');
    
    console.log('Nombre de BL:', bonsLivraison.length);
    console.log('BL disponibles:', bonsLivraison);
    
    // Forcer la mise à jour des statistiques
    updateStats();
    
    // Vérifier les éléments DOM
    const elements = {
        'total-bons': totalBonsEl,
        'total-montant': totalMontantEl,
        'total-quantite': totalQuantiteEl
    };
    
    console.log('Éléments DOM des statistiques:');
    Object.entries(elements).forEach(([id, element]) => {
        if (element) {
            console.log(`- ${id}: "${element.textContent}"`);
        } else {
            console.log(`- ${id}: ÉLÉMENT NON TROUVÉ`);
        }
    });
    
    alert(`📊 Test Statistiques\n\nNombre de BL: ${bonsLivraison.length}\n\nRésultats dans la console (F12)`);
}

// Rendre les fonctions globales
window.editBon = editBon;
window.deleteBon = deleteBon;
window.removeMedicament = removeMedicament;
window.testStatistiques = testStatistiques;

// Raccourcis clavier
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        testStatistiques();
    }
});
